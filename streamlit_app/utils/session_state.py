"""
Session State Management for Streamlit Application

This module handles the initialization and management of Streamlit session state
variables used throughout the epidemiology research application.
"""

import streamlit as st
from typing import Dict, Any, Optional

def initialize_session_state():
    """Initialize all session state variables with default values."""
    
    # Navigation and UI state
    if "current_page" not in st.session_state:
        st.session_state.current_page = "research"
    
    # Database connection state
    if "db_connection_checked" not in st.session_state:
        st.session_state.db_connection_checked = False
    
    if "db_connection_status" not in st.session_state:
        st.session_state.db_connection_status = {"connected": False, "error": None}
    
    # Research interface state
    if "research_form_data" not in st.session_state:
        st.session_state.research_form_data = {
            "disease_name": "ulcerative colitis",
            "country": "US",
            "time_period": "1970-2025",
            "num_works": 50
        }
    
    if "research_progress" not in st.session_state:
        st.session_state.research_progress = {
            "is_running": False,
            "current_step": "Idle",
            "disease_name": None,
            "country": None,
            "total_steps": 0,
            "completed_steps": 0,
            "step_details": {},
            "results": {},
            "errors": []
        }
    
    # Dashboard state
    if "dashboard_filters" not in st.session_state:
        st.session_state.dashboard_filters = {
            "selected_diseases": [],
            "selected_countries": [],
            "data_type": "all"
        }
    
    if "dashboard_data" not in st.session_state:
        st.session_state.dashboard_data = {
            "point_prevalence": None,
            "period_prevalence": None,
            "incidence_rate": None,
            "cumulative_incidence": None,
            "last_updated": None
        }
    
    # Progress tracking for long-running operations
    if "operation_status" not in st.session_state:
        st.session_state.operation_status = {
            "screening": {"total": 0, "completed": 0, "status": "idle"},
            "download": {"total": 0, "completed": 0, "status": "idle"},
            "extraction": {"total": 0, "completed": 0, "status": "idle"}
        }

def reset_research_progress():
    """Reset research progress to initial state."""
    st.session_state.research_progress = {
        "is_running": False,
        "current_step": "Idle",
        "disease_name": None,
        "country": None,
        "total_steps": 0,
        "completed_steps": 0,
        "step_details": {},
        "results": {},
        "errors": []
    }
    
    st.session_state.operation_status = {
        "screening": {"total": 0, "completed": 0, "status": "idle"},
        "download": {"total": 0, "completed": 0, "status": "idle"},
        "extraction": {"total": 0, "completed": 0, "status": "idle"}
    }

def update_research_progress(
    step: str, 
    details: Optional[Dict[str, Any]] = None,
    increment_completed: bool = False
):
    """Update research progress with current step and details."""
    st.session_state.research_progress["current_step"] = step
    
    if details:
        st.session_state.research_progress["step_details"][step] = details
    
    if increment_completed:
        st.session_state.research_progress["completed_steps"] += 1

def update_operation_status(operation: str, total: int = None, completed: int = None, status: str = None):
    """Update the status of a specific operation (screening, download, extraction)."""
    if operation in st.session_state.operation_status:
        if total is not None:
            st.session_state.operation_status[operation]["total"] = total
        if completed is not None:
            st.session_state.operation_status[operation]["completed"] = completed
        if status is not None:
            st.session_state.operation_status[operation]["status"] = status

def get_research_progress() -> Dict[str, Any]:
    """Get current research progress."""
    return st.session_state.research_progress

def get_operation_status(operation: str) -> Dict[str, Any]:
    """Get status of a specific operation."""
    return st.session_state.operation_status.get(operation, {"total": 0, "completed": 0, "status": "idle"})

def set_research_running(disease_name: str, country: str, total_steps: int = 6):
    """Set research as running with initial parameters."""
    st.session_state.research_progress.update({
        "is_running": True,
        "disease_name": disease_name,
        "country": country,
        "total_steps": total_steps,
        "completed_steps": 0,
        "current_step": "Starting research...",
        "step_details": {},
        "results": {},
        "errors": []
    })

def set_research_completed():
    """Mark research as completed."""
    st.session_state.research_progress["is_running"] = False
    st.session_state.research_progress["current_step"] = "Completed"

def add_research_error(error: str):
    """Add an error to the research progress."""
    st.session_state.research_progress["errors"].append(error)

def clear_dashboard_data():
    """Clear cached dashboard data to force refresh."""
    st.session_state.dashboard_data = {
        "point_prevalence": None,
        "period_prevalence": None,
        "incidence_rate": None,
        "cumulative_incidence": None,
        "last_updated": None
    }
