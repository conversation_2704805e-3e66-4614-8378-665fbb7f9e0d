"""
Data Visualization Dashboard Page

This page provides an interactive dashboard for displaying and filtering
extracted prevalence and incidence data from the epidemiology research system.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from typing import List, Dict, Any
from datetime import datetime

# Import database utilities
from streamlit.utils.database import (
    get_available_diseases,
    get_available_countries,
    get_prevalence_data,
    get_incidence_data,
    get_data_summary_stats,
    clear_data_cache
)

def data_dashboard_page():
    """Main data dashboard page."""
    
    st.title("📊 Data Visualization Dashboard")
    st.markdown("Interactive dashboard for epidemiological data analysis")
    
    # Sidebar filters
    setup_filters()
    
    # Main dashboard content
    col1, col2 = st.columns([2, 1])
    
    with col1:
        display_data_visualizations()
    
    with col2:
        display_summary_statistics()
        display_filter_info()

def setup_filters():
    """Setup sidebar filters for data selection."""
    
    st.sidebar.markdown("### 🔍 Data Filters")
    
    # Refresh data button
    if st.sidebar.button("🔄 Refresh Data", help="Clear cache and reload data"):
        clear_data_cache()
        st.rerun()
    
    # Disease filter
    available_diseases = get_available_diseases()
    selected_diseases = st.sidebar.multiselect(
        "Select Diseases",
        options=available_diseases,
        default=st.session_state.dashboard_filters["selected_diseases"],
        help="Filter data by specific diseases"
    )
    
    # Country filter
    available_countries = get_available_countries()
    selected_countries = st.sidebar.multiselect(
        "Select Countries",
        options=available_countries,
        default=st.session_state.dashboard_filters["selected_countries"],
        help="Filter data by specific countries"
    )
    
    # Data type filter
    data_type = st.sidebar.selectbox(
        "Data Type",
        options=["all", "prevalence", "incidence"],
        index=["all", "prevalence", "incidence"].index(st.session_state.dashboard_filters["data_type"]),
        help="Select which type of data to display"
    )
    
    # Update session state
    st.session_state.dashboard_filters.update({
        "selected_diseases": selected_diseases,
        "selected_countries": selected_countries,
        "data_type": data_type
    })

def display_data_visualizations():
    """Display the main data visualizations."""
    
    filters = st.session_state.dashboard_filters
    
    # Get filtered data
    if filters["data_type"] in ["all", "prevalence"]:
        point_prev_df, period_prev_df = get_prevalence_data(
            diseases=filters["selected_diseases"] if filters["selected_diseases"] else None,
            countries=filters["selected_countries"] if filters["selected_countries"] else None
        )
    else:
        point_prev_df, period_prev_df = pd.DataFrame(), pd.DataFrame()
    
    if filters["data_type"] in ["all", "incidence"]:
        incidence_rate_df, cumulative_inc_df = get_incidence_data(
            diseases=filters["selected_diseases"] if filters["selected_diseases"] else None,
            countries=filters["selected_countries"] if filters["selected_countries"] else None
        )
    else:
        incidence_rate_df, cumulative_inc_df = pd.DataFrame(), pd.DataFrame()
    
    # Display visualizations based on available data
    if not point_prev_df.empty:
        display_prevalence_charts(point_prev_df, period_prev_df)
    
    if not incidence_rate_df.empty:
        display_incidence_charts(incidence_rate_df, cumulative_inc_df)
    
    # Display data tables
    display_data_tables(point_prev_df, period_prev_df, incidence_rate_df, cumulative_inc_df)

def display_prevalence_charts(point_prev_df: pd.DataFrame, period_prev_df: pd.DataFrame):
    """Display prevalence data visualizations."""
    
    st.markdown("### 📈 Prevalence Data")
    
    # Point prevalence chart
    if not point_prev_df.empty:
        st.markdown("#### Point Prevalence")
        
        fig = px.scatter(
            point_prev_df,
            x="year",
            y="point_prevalence_percent",
            color="disease_name",
            size="n_population",
            hover_data=["country", "condition", "n_cases"],
            title="Point Prevalence Over Time"
        )
        fig.update_layout(
            xaxis_title="Year",
            yaxis_title="Prevalence (%)",
            height=400
        )
        st.plotly_chart(fig, use_container_width=True)
    
    # Period prevalence chart
    if not period_prev_df.empty:
        st.markdown("#### Period Prevalence")
        
        fig = px.bar(
            period_prev_df.groupby(["disease_name", "country"])["period_prevalence_percent"].mean().reset_index(),
            x="country",
            y="period_prevalence_percent",
            color="disease_name",
            title="Average Period Prevalence by Country"
        )
        fig.update_layout(
            xaxis_title="Country",
            yaxis_title="Prevalence (%)",
            height=400
        )
        st.plotly_chart(fig, use_container_width=True)

def display_incidence_charts(incidence_rate_df: pd.DataFrame, cumulative_inc_df: pd.DataFrame):
    """Display incidence data visualizations."""
    
    st.markdown("### 📊 Incidence Data")
    
    # Incidence rate chart
    if not incidence_rate_df.empty:
        st.markdown("#### Incidence Rate")
        
        fig = px.line(
            incidence_rate_df,
            x="start_year",
            y="incidence_rate",
            color="disease_name",
            line_group="country",
            hover_data=["end_year", "n_cases", "person_years"],
            title="Incidence Rate Over Time"
        )
        fig.update_layout(
            xaxis_title="Year",
            yaxis_title="Incidence Rate (per 100,000 person-years)",
            height=400
        )
        st.plotly_chart(fig, use_container_width=True)
    
    # Cumulative incidence chart
    if not cumulative_inc_df.empty:
        st.markdown("#### Cumulative Incidence")
        
        fig = px.box(
            cumulative_inc_df,
            x="disease_name",
            y="cumulative_incidence_percent",
            color="country",
            title="Cumulative Incidence Distribution"
        )
        fig.update_layout(
            xaxis_title="Disease",
            yaxis_title="Cumulative Incidence (%)",
            height=400
        )
        st.plotly_chart(fig, use_container_width=True)

def display_data_tables(point_prev_df: pd.DataFrame, period_prev_df: pd.DataFrame, 
                       incidence_rate_df: pd.DataFrame, cumulative_inc_df: pd.DataFrame):
    """Display data tables with filtering and sorting."""
    
    st.markdown("### 📋 Data Tables")
    
    # Create tabs for different data types
    tabs = []
    tab_data = []
    
    if not point_prev_df.empty:
        tabs.append("Point Prevalence")
        tab_data.append(point_prev_df)
    
    if not period_prev_df.empty:
        tabs.append("Period Prevalence")
        tab_data.append(period_prev_df)
    
    if not incidence_rate_df.empty:
        tabs.append("Incidence Rate")
        tab_data.append(incidence_rate_df)
    
    if not cumulative_inc_df.empty:
        tabs.append("Cumulative Incidence")
        tab_data.append(cumulative_inc_df)
    
    if tabs:
        tab_objects = st.tabs(tabs)
        
        for i, (tab, df) in enumerate(zip(tab_objects, tab_data)):
            with tab:
                # Display summary
                st.write(f"**Total Records:** {len(df)}")
                
                # Display table with pagination
                if len(df) > 100:
                    st.info(f"Showing first 100 of {len(df)} records. Use filters to narrow results.")
                    display_df = df.head(100)
                else:
                    display_df = df
                
                st.dataframe(
                    display_df,
                    use_container_width=True,
                    hide_index=True
                )
                
                # Download button
                csv = df.to_csv(index=False)
                st.download_button(
                    label=f"📥 Download {tabs[i]} Data (CSV)",
                    data=csv,
                    file_name=f"{tabs[i].lower().replace(' ', '_')}_data.csv",
                    mime="text/csv"
                )

def display_summary_statistics():
    """Display summary statistics."""
    
    st.markdown("### 📊 Summary Statistics")
    
    stats = get_data_summary_stats()
    
    if stats:
        st.metric("Total Articles", stats.get("total_articles", 0))
        st.metric("Point Prevalence Records", stats.get("point_prevalence", 0))
        st.metric("Period Prevalence Records", stats.get("period_prevalence", 0))
        st.metric("Incidence Rate Records", stats.get("incidence_rate", 0))
        st.metric("Cumulative Incidence Records", stats.get("cumulative_incidence", 0))
    else:
        st.info("No data available")

def display_filter_info():
    """Display current filter information."""
    
    st.markdown("### 🔍 Current Filters")
    
    filters = st.session_state.dashboard_filters
    
    if filters["selected_diseases"]:
        st.write("**Diseases:**")
        for disease in filters["selected_diseases"]:
            st.write(f"• {disease}")
    else:
        st.write("**Diseases:** All")
    
    if filters["selected_countries"]:
        st.write("**Countries:**")
        for country in filters["selected_countries"]:
            st.write(f"• {country}")
    else:
        st.write("**Countries:** All")
    
    st.write(f"**Data Type:** {filters['data_type'].title()}")
    
    # Last updated info
    st.markdown("---")
    st.caption(f"Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
