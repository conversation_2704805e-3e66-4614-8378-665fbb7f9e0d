"""
Progress Tracker Component

This component provides real-time progress tracking for long-running operations
in the epidemiology research system, including screening, downloading, and extraction.
"""

import streamlit as st
from typing import Dict, Any
from streamlit.utils.session_state import get_operation_status

class ProgressTracker:
    """Component for displaying operation progress with real-time updates."""
    
    def __init__(self):
        self.operations = ["screening", "download", "extraction"]
        self.operation_labels = {
            "screening": "📋 Article Screening",
            "download": "📥 Fulltext Download", 
            "extraction": "🔬 Data Extraction"
        }
    
    def display_progress(self):
        """Display progress for all operations."""
        
        st.markdown("### 📈 Operation Progress")
        
        for operation in self.operations:
            self._display_operation_progress(operation)
    
    def _display_operation_progress(self, operation: str):
        """Display progress for a specific operation."""
        
        status = get_operation_status(operation)
        label = self.operation_labels.get(operation, operation.title())
        
        # Skip if operation hasn't started
        if status["status"] == "idle" and status["total"] == 0:
            return
        
        with st.container():
            col1, col2 = st.columns([3, 1])
            
            with col1:
                st.markdown(f"**{label}**")
                
                if status["total"] > 0:
                    progress = status["completed"] / status["total"]
                    st.progress(progress, text=f"{status['completed']}/{status['total']} completed")
                else:
                    st.progress(0, text="Initializing...")
            
            with col2:
                self._display_status_badge(status["status"])
            
            # Additional details based on operation
            if operation == "screening" and status["status"] == "running":
                st.caption("🔍 Analyzing abstracts for epidemiological relevance...")
            elif operation == "download" and status["status"] == "running":
                st.caption("📄 Downloading full-text articles from PubMed Central...")
            elif operation == "extraction" and status["status"] == "running":
                st.caption("📊 Extracting prevalence and incidence data...")
    
    def _display_status_badge(self, status: str):
        """Display a status badge with appropriate styling."""
        
        status_styles = {
            "idle": ("⚪", "Idle"),
            "running": ("🟡", "Running"),
            "completed": ("🟢", "Completed"),
            "failed": ("🔴", "Failed")
        }
        
        icon, text = status_styles.get(status, ("⚪", "Unknown"))
        st.markdown(f"{icon} {text}")
    
    def update_screening_progress(self, total: int, completed: int):
        """Update screening operation progress."""
        from streamlit.utils.session_state import update_operation_status
        
        status = "running" if completed < total else "completed"
        update_operation_status("screening", total=total, completed=completed, status=status)
    
    def update_download_progress(self, total: int, completed: int):
        """Update download operation progress."""
        from streamlit.utils.session_state import update_operation_status
        
        status = "running" if completed < total else "completed"
        update_operation_status("download", total=total, completed=completed, status=status)
    
    def update_extraction_progress(self, total: int, completed: int):
        """Update extraction operation progress."""
        from streamlit.utils.session_state import update_operation_status
        
        status = "running" if completed < total else "completed"
        update_operation_status("extraction", total=total, completed=completed, status=status)

class StreamlitProgressGenerator:
    """Generator wrapper that updates Streamlit progress for any generator."""
    
    def __init__(self, generator, operation_type: str, total_items: int):
        self.generator = generator
        self.operation_type = operation_type
        self.total_items = total_items
        self.completed_items = 0
        self.tracker = ProgressTracker()
    
    def __iter__(self):
        return self
    
    def __next__(self):
        try:
            result = next(self.generator)
            self.completed_items += 1
            
            # Update progress based on operation type
            if self.operation_type == "screening":
                self.tracker.update_screening_progress(self.total_items, self.completed_items)
            elif self.operation_type == "download":
                self.tracker.update_download_progress(self.total_items, self.completed_items)
            elif self.operation_type == "extraction":
                self.tracker.update_extraction_progress(self.total_items, self.completed_items)
            
            return result
        except StopIteration:
            # Mark operation as completed
            from streamlit.utils.session_state import update_operation_status
            update_operation_status(self.operation_type, status="completed")
            raise

def create_progress_generator(generator, operation_type: str, total_items: int):
    """Create a progress-tracking generator wrapper."""
    return StreamlitProgressGenerator(generator, operation_type, total_items)

# Progress display utilities
def display_step_progress(step_name: str, current: int, total: int, details: str = ""):
    """Display progress for a specific step."""
    
    progress = current / max(total, 1)
    
    col1, col2 = st.columns([3, 1])
    with col1:
        st.markdown(f"**{step_name}**")
        st.progress(progress, text=f"{current}/{total} completed")
        if details:
            st.caption(details)
    
    with col2:
        st.metric("Progress", f"{progress:.1%}")

def display_operation_summary(results: Dict[str, Any]):
    """Display a summary of operation results."""
    
    st.markdown("### 📊 Operation Summary")
    
    # Create metrics for each operation
    col1, col2, col3 = st.columns(3)
    
    with col1:
        screening_results = results.get("screening", {})
        st.metric(
            "Articles Screened",
            screening_results.get("total", 0),
            delta=screening_results.get("relevant", 0)
        )
    
    with col2:
        download_results = results.get("download", {})
        st.metric(
            "Articles Downloaded",
            download_results.get("successful", 0),
            delta=download_results.get("failed", 0) * -1 if download_results.get("failed", 0) > 0 else None
        )
    
    with col3:
        extraction_results = results.get("extraction", {})
        st.metric(
            "Data Points Extracted",
            extraction_results.get("total_extracted", 0),
            delta=extraction_results.get("errors", 0) * -1 if extraction_results.get("errors", 0) > 0 else None
        )
