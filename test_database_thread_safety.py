#!/usr/bin/env python3
"""
Test script to verify that database connections work properly in background threads.
"""

import sys
import os
import threading
import time

# Add src directory to Python path
sys.path.append('src')

def test_database_thread_safety():
    """Test that database connections work properly in background threads."""
    
    print("🧪 Testing Database Thread Safety")
    print("=" * 50)
    
    # Import database functions
    from streamlit_app.utils.database import get_article_manager_for_thread, get_extraction_manager_for_thread
    
    success_count = 0
    error_count = 0
    errors = []
    
    def test_database_in_thread(thread_id):
        """Test database operations in a background thread."""
        nonlocal success_count, error_count, errors
        
        try:
            print(f"Thread {thread_id}: Starting database test...")
            
            # Test article manager
            article_manager = get_article_manager_for_thread()
            if article_manager is None:
                raise Exception("Article manager is None")
            
            print(f"Thread {thread_id}: Article manager created successfully")
            
            # Test extraction manager
            extraction_manager = get_extraction_manager_for_thread()
            if extraction_manager is None:
                raise Exception("Extraction manager is None")
            
            print(f"Thread {thread_id}: Extraction manager created successfully")
            
            # Test basic database operations
            stats = article_manager.get_article_statistics()
            print(f"Thread {thread_id}: Article statistics: {stats}")
            
            # Test disease-specific queries
            articles_needing_screening = article_manager.get_articles_needing_screening("test_disease")
            print(f"Thread {thread_id}: Found {len(articles_needing_screening)} articles needing screening")
            
            articles_needing_fulltext = article_manager.get_articles_needing_fulltext("test_disease")
            print(f"Thread {thread_id}: Found {len(articles_needing_fulltext)} articles needing fulltext")
            
            articles_needing_extraction = article_manager.get_articles_needing_extraction("test_disease")
            print(f"Thread {thread_id}: Found {len(articles_needing_extraction)} articles needing extraction")
            
            success_count += 1
            print(f"Thread {thread_id}: ✅ Database operations completed successfully")
            
        except Exception as e:
            error_count += 1
            error_msg = f"Thread {thread_id}: ❌ Database operations failed: {e}"
            errors.append(error_msg)
            print(error_msg)
            import traceback
            traceback.print_exc()
    
    # Test with multiple threads to ensure thread safety
    threads = []
    num_threads = 3
    
    print(f"\n🚀 Starting {num_threads} database test threads...")
    
    for i in range(num_threads):
        thread = threading.Thread(target=test_database_in_thread, args=(i+1,), daemon=True)
        threads.append(thread)
        thread.start()
    
    # Wait for all threads to complete
    for thread in threads:
        thread.join(timeout=30)
    
    # Check if any threads are still alive
    alive_threads = [t for t in threads if t.is_alive()]
    if alive_threads:
        print(f"⚠️ {len(alive_threads)} threads are still running after timeout")
    
    print(f"\n📋 Database Thread Safety Test Results:")
    print(f"   Successful threads: {success_count}")
    print(f"   Failed threads: {error_count}")
    print(f"   Total threads: {num_threads}")
    
    if errors:
        print("❌ Errors encountered:")
        for error in errors:
            print(f"   - {error}")
    
    if success_count == num_threads and error_count == 0:
        print("✅ Database thread safety test PASSED!")
        return True
    else:
        print("❌ Database thread safety test FAILED!")
        return False

def test_progress_manager_thread_safety():
    """Test that the progress manager works correctly across threads."""
    
    print("\n🧪 Testing Progress Manager Thread Safety")
    print("=" * 50)
    
    from streamlit_app.utils.progress_manager import get_progress_manager
    
    progress_manager = get_progress_manager()
    progress_manager.reset_progress()
    
    success_count = 0
    error_count = 0
    
    def test_progress_in_thread(thread_id):
        """Test progress manager operations in a background thread."""
        nonlocal success_count, error_count
        
        try:
            print(f"Progress Thread {thread_id}: Starting progress test...")
            
            # Test progress updates
            progress_manager.update_progress(f"Thread {thread_id} step 1", increment_completed=True)
            time.sleep(0.1)
            
            progress_manager.add_result(f"thread_{thread_id}_result", {"test": "data"})
            time.sleep(0.1)
            
            progress_manager.update_operation_status(f"operation_{thread_id}", total=10, completed=5, status="running")
            time.sleep(0.1)
            
            # Get progress
            progress = progress_manager.get_progress()
            print(f"Progress Thread {thread_id}: Current progress: {progress['current_step']}")
            
            success_count += 1
            print(f"Progress Thread {thread_id}: ✅ Progress operations completed successfully")
            
        except Exception as e:
            error_count += 1
            print(f"Progress Thread {thread_id}: ❌ Progress operations failed: {e}")
            import traceback
            traceback.print_exc()
    
    # Test with multiple threads
    threads = []
    num_threads = 3
    
    print(f"\n🚀 Starting {num_threads} progress test threads...")
    
    for i in range(num_threads):
        thread = threading.Thread(target=test_progress_in_thread, args=(i+1,), daemon=True)
        threads.append(thread)
        thread.start()
    
    # Wait for all threads to complete
    for thread in threads:
        thread.join(timeout=10)
    
    print(f"\n📋 Progress Manager Thread Safety Test Results:")
    print(f"   Successful threads: {success_count}")
    print(f"   Failed threads: {error_count}")
    print(f"   Total threads: {num_threads}")
    
    if success_count == num_threads and error_count == 0:
        print("✅ Progress manager thread safety test PASSED!")
        return True
    else:
        print("❌ Progress manager thread safety test FAILED!")
        return False

if __name__ == "__main__":
    print("🚀 Starting Database and Progress Manager Thread Safety Tests")
    print("=" * 70)
    
    db_success = test_database_thread_safety()
    progress_success = test_progress_manager_thread_safety()
    
    if db_success and progress_success:
        print("\n🎉 All thread safety tests completed successfully!")
    else:
        print("\n❌ Some thread safety tests failed!")
        sys.exit(1)
