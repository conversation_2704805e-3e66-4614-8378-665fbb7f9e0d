from dotenv import load_dotenv
load_dotenv()

# Add project root to Python path for imports
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import random
import logging
from textwrap import dedent
from typing import Dict, Any
from agno.agent import Agent
from agno.models.openai import OpenAIChat
import json
import os
from agno.tools.reasoning import ReasoningTools

from src.helpers.epidata_models import PeriodPrevalenceRecord
from src.db import DatabaseConnection, ExtractionManager

# Set up logging
logger = logging.getLogger(__name__)

config = json.load(open(f"{project_root}/src/config.json"))

## 1) Period Prevalence Extraction Agent
period_prevalence_config = config["PeriodPrevalenceExtractionAgent"]

def record_period_prevalence_datapoints(session_state:Dict[str, Any], datapoint:PeriodPrevalenceRecord, confidence_value:float) -> str:
    """
    Record period prevalence datapoint from fulltext article.

    Args:
        datapoint: Pydantic model for Period prevalence datapoint
        confidence_value: Confidence value for the datapoint
    """
    
    if confidence_value >= config["PeriodPrevalenceExtractionAgent"]["confidence_threshold"]:
        session_state["period_prevalence_datapoints"].append(datapoint)
        status = f"{len(session_state['period_prevalence_datapoints'])} period prevalence datapoints have been recorded"
    else:
        status = f"Period prevalence datapoint not recorded due to low confidence value"
    return status

def save_period_prevalence_datapoints(session_state:Dict[str, Any]):
    """
    Save all recorded period prevalence datapoints into the database.
    """
    try:
        db_connection = DatabaseConnection()
        db_connection.connect()
        extraction_manager = ExtractionManager(db_connection=db_connection)

        num_datapoints = len(session_state["period_prevalence_datapoints"])
        extraction_manager.save_period_prevalence_records(
            session_state["period_prevalence_datapoints"],
            session_state["pmid"],
            session_state["disease_name"],
            session_state["pmcid"]
        )
        db_connection.close()
        status = f"{num_datapoints} Period prevalence datapoints have been saved to the database"
    except: 
        status = f"Failed to save period prevalence datapoints to the database"
    return status

def get_period_prevalence_extraction_instructions() -> str:
    """
    Get instructions for period prevalence extraction agent.
    """
    return dedent(
        """
        You are an expert epidemiologist and medical researcher specializing in extracting period prevalence data from scientific articles. Your task is to systematically analyze epidemiological literature and extract structured period prevalence datapoints.

        ## OVERVIEW
        Period prevalence represents the proportion of a population that has a specific condition at any time during a specified time period (typically measured over a range of years, such as 2010-2015). You will extract these datapoints using a structured approach with the available tools.

        ## TOOL USAGE INSTRUCTIONS

        ### 1. REASONING TOOLS
        Use the ReasoningTools throughout your analysis:

        **think()**: Use this to:
        - Plan your approach before reading the article
        - Break down complex sections of the article
        - Consider multiple interpretations of data
        - Reflect on extracted information before recording

        **analyze()**: Use this to:
        - Examine statistical data and methodology sections
        - Evaluate data quality and reliability
        - Compare different prevalence estimates within the same study
        - Assess demographic stratifications and subgroup analyses

        ### 2. DATA EXTRACTION WORKFLOW

        **STEP 1: Initial Analysis**
        - Use think() to plan your extraction strategy
        - Use analyze() to understand the study design, population, and methodology
        - Identify relevant sections (Results, Tables, Figures, Discussion)

        **STEP 2: Systematic Data Identification**
        For each potential datapoint, use analyze() to evaluate:
        - Is this truly period prevalence (not incidence, point prevalence, or lifetime prevalence)?
        - Is the measurement over a specified time range (start_year to end_year)?
        - Are the numerator and denominator clearly defined?
        - What population demographics are specified?

        **STEP 3: Data Recording**
        Use record_period_prevalence_datapoints() for each valid datapoint with:
        - Complete PeriodPrevalenceRecord with all available fields
        - Confidence value between 0.0-1.0 based on data quality
        - Higher confidence (0.8-1.0) for: clear methodology, large sample sizes, well-defined populations
        - Lower confidence (0.5-0.7) for: unclear definitions, small samples, limited demographic data

        **STEP 4: Final Database Save**
        Use save_period_prevalence_datapoints() once at the end to persist all extracted data.

        ### 3. PERIOD PREVALENCE RECORD STRUCTURE

        Extract the following fields for each datapoint:

        **Required Fields:**
        - condition: Disease/condition name (should match the target disease)
        - period_prevalence_percent: Prevalence as percentage (0-100) over the time period
        - start_year: Start year of the measurement period
        - end_year: End year of the measurement period

        **Optional but Important Fields:**
        - ci_lower_percent, ci_upper_percent: 95% confidence intervals
        - n_cases: Number of unique individuals with the condition during the period
        - n_population: Total population assessed during the period
        - country: ISO code (preferred) or country name
        - age_group: Format as 'all', 'X-Y', '>=X', or '<X'
        - gender: 'male', 'female', 'both', or 'unknown'
        - ethnicity: Specific ethnicity or 'all'/'unknown'

        ### 4. DATA QUALITY ASSESSMENT

        **High Confidence (0.8-1.0):**
        - Clear period prevalence definition and calculation
        - Large representative sample (n>1000)
        - Well-defined time period with clear start and end years
        - Complete demographic information
        - Peer-reviewed source with clear methodology

        **Medium Confidence (0.6-0.7):**
        - Generally clear but some ambiguity in definitions
        - Moderate sample size (100-1000)
        - Some missing demographic details
        - Limited methodological description

        **Low Confidence (<0.6):**
        - Unclear prevalence definition
        - Small sample size (<100)
        - Significant missing information
        - Questionable methodology

        ### 5. EXTRACTION GUIDELINES

        **DO Extract:**
        - Longitudinal study prevalence over specified time ranges
        - Registry-based prevalence for multi-year periods
        - Survey-based prevalence estimates spanning multiple years
        - Subgroup analyses by age, gender, ethnicity, geography over time periods

        **DON'T Extract:**
        - Incidence rates (new cases over time)
        - Point prevalence (at single time points)
        - Lifetime prevalence
        - Case reports or case series
        - Prevalence trends without specific time period boundaries

        **Multiple Datapoints:**
        - Extract separate records for different demographic subgroups
        - Extract separate records for different time periods
        - Extract separate records for different geographic regions

        ### 6. SYSTEMATIC APPROACH

        1. **Initialize**: Use think() to outline your extraction plan
        2. **Scan**: Use analyze() to identify all potential prevalence data in tables, figures, and text
        3. **Evaluate**: For each potential datapoint, use think() to assess if it meets period prevalence criteria
        4. **Extract**: Use analyze() to carefully extract all relevant values and metadata
        5. **Record**: Use record_period_prevalence_datapoints() with appropriate confidence scores
        6. **Validate**: Use think() to review all extracted datapoints for completeness and accuracy
        7. **Save**: Use save_period_prevalence_datapoints() to persist all data
        8. **Summarize**: State the total number of period prevalence datapoints extracted and successfully saved to the database.

        ### 7. KEY DIFFERENCES FROM POINT PREVALENCE

        - **Time Period**: Period prevalence spans a range of years (e.g., 2010-2015), not a single year
        - **Numerator**: Counts unique individuals who had the condition at ANY time during the period
        - **Interpretation**: Higher than point prevalence because it captures those who had the condition at any point during the timeframe

        ### 8. COMMON PITFALLS TO AVOID

        - Don't extract cumulative incidence as period prevalence
        - Don't record the same datapoint multiple times
        - Don't extract prevalence from studies without clear time period boundaries
        - Don't assume demographic characteristics if not explicitly stated
        - Don't extract point prevalence (single year) as period prevalence
        - Don't extract lifetime prevalence as period prevalence

        ## FINAL REMINDERS

        - Use reasoning tools extensively to ensure accuracy
        - Extract only period prevalence (not other epidemiological measures)
        - Record each unique datapoint separately
        - Assign appropriate confidence scores
        - Save all datapoints once at the end
        - Focus on the target disease but be aware of related conditions
        - Prioritize recent, high-quality studies with large sample sizes
        - Ensure time periods have clearly defined start and end years
        """
    )

def extract_period_prevalence_from_article(disease_name:str, pmcid: str, pmid:str) -> Dict[str,Any]:
    """
    Extract period prevalence datapoints from an full article markdown file.
    """

    pmcid_number = pmcid.split("/")[-1]
    with open(f"{project_root}/data/publications/{pmcid_number}.md", "r") as f:
        fulltext_article = f.read()

    tools = []
    if period_prevalence_config["use_reasoning"]:
        tools.append(ReasoningTools(enable_think=True, enable_analyze=True))
    tools.append(record_period_prevalence_datapoints)
    tools.append(save_period_prevalence_datapoints)

    agent = Agent(
        name="Period Prevalence Extraction Agent",
        model=OpenAIChat(id=period_prevalence_config["model_id"]),
        description="Analyze, record and save period prevalence datapoints into epidemiology research database from an full article markdown file of epidemiology article.",
        tools=tools,
        instructions=get_period_prevalence_extraction_instructions(),
        delay_between_retries=random.randint(1,3),
        exponential_backoff=True,
        debug_mode=period_prevalence_config["debug_mode"],
        telemetry=False
    )
    
    status_object = {
        "disease": disease_name,
        "PMID": pmid,
        "PMCID": pmcid,
        "task": "period_prevalence_extraction",
        "status": "RUNNING",
        "extracted_datapoints": 0,
        "summary": ""
    }
    
    # Initialize database connection for updating extraction status
    db_connection = DatabaseConnection()
    extraction_manager = None
    try:
        db_connection.connect()
        extraction_manager = ExtractionManager(db_connection)
        
        # Update status to "running" when starting extraction
        extraction_manager.update_extraction_status(
            pmid=pmid,
            pmcid=pmcid,
            task_type="period_prevalence_extraction",
            status="running"
        )
        status_object['status'] = "RUNNING"
        
    except Exception as e:
        logger.warning(f"Could not update extraction status to running: {e}")
    
    try:
        current_session = {"pmid": pmid, "pmcid": pmcid, "disease_name": disease_name,  "period_prevalence_datapoints":[]}
        response = agent.run(dedent(f"""
            Read the attached fulltext epidemiology article about {disease_name}. Think step-by-step and analyze the article for carefully extracting period prevalence datapoints.

            FULLTEXT ARTICLE:
            {fulltext_article}
            """),
            session_state=current_session
        )
        if isinstance(response.content, str):
            status_object['status'] = "SUCCESS"
            status_object['extracted_datapoints'] = len(current_session['period_prevalence_datapoints'])
            status_object['summary'] = response.content
        else:
            status_object['status'] = "FAILED"
            status_object['extracted_datapoints'] = 0
            status_object['summary'] = f"FAILED: Period Prevalence Extraction Error Type: {type(response)}"
    except Exception as e:
        status_object['status'] = "FAILED"
        status_object['extracted_datapoints'] = 0
        status_object['summary'] = f"FAILED: Exception during period prevalence extraction from article - {e}"
    finally:
        # Update dim_extraction table with final results
        if extraction_manager:
            try:
                extraction_manager.update_extraction_from_response(status_object)
                logger.debug(f"Updated dim_extraction table for PMID {pmid}")
            except Exception as e:
                logger.warning(f"Failed to update dim_extraction table: {e}")
        
        # Close database connection
        if db_connection and db_connection.engine:
            db_connection.close()
    
    return status_object
    

if __name__ == "__main__":
    # example usage
    pmid = "PMC4068111"
    pmcid = "PMC4068111"
    disease_name = "Diabetes Mellitus"
    print(extract_period_prevalence_from_article(disease_name, pmcid, pmid))
