from dotenv import load_dotenv
load_dotenv()

# Add project root to Python path for imports
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import random
import logging
from textwrap import dedent
from typing import Dict, Any
from agno.agent import Agent
from agno.models.openai import OpenAIChat
import json
from agno.tools.reasoning import ReasoningTools

from src.helpers.epidata_models import IncidenceRateRecord, ExtractionSummary
from src.db import DatabaseConnection, ExtractionManager

# Set up logging
logger = logging.getLogger(__name__)

config = json.load(open(f"{project_root}/src/config.json"))

## 1) Incidence Rate Extraction Agent
incidence_rate_config = config["IncidenceRateExtractionAgent"]

def record_incidence_rate_datapoints(session_state:Dict[str, Any], datapoint:IncidenceRateRecord, confidence_value:float) -> str:
    """
    Record incidence rate datapoint from fulltext article.

    Args:
        datapoint: Pydantic model for Incidence rate datapoint
        confidence_value: Confidence value for the datapoint
    """
    
    if confidence_value >= config["IncidenceRateExtractionAgent"]["confidence_threshold"]:
        session_state["incidence_rate_datapoints"].append(datapoint)
        status = f"{len(session_state['incidence_rate_datapoints'])} incidence rate datapoints have been recorded"
    else:
        status = f"Incidence rate datapoint not recorded due to low confidence value"
    return status

def save_incidence_rate_datapoints(session_state:Dict[str, Any]):
    """
    Save all recorded incidence rate datapoints into the database.
    """
    try:
        db_connection = DatabaseConnection()
        db_connection.connect()
        extraction_manager = ExtractionManager(db_connection=db_connection)

        num_datapoints = len(session_state["incidence_rate_datapoints"])
        extraction_manager.save_incidence_rate_records(
            session_state["incidence_rate_datapoints"],
            session_state["pmid"],
            session_state["disease_name"],
            session_state["pmcid"]
        )
        db_connection.close()
        status = f"{num_datapoints} Incidence rate datapoints have been saved to the database"
    except: 
        status = f"Failed to save incidence rate datapoints to the database"
    return status

def get_incidence_rate_extraction_instructions() -> str:
    """
    Get instructions for incidence rate extraction agent.
    """
    return dedent(
        """
        You are an expert epidemiologist and medical researcher specializing in extracting incidence rate data from scientific articles. Your task is to systematically analyze epidemiological literature and extract structured incidence rate datapoints.

        ## OVERVIEW
        Incidence rate represents the number of new cases of a condition per unit of person-time (typically per 100,000 person-years) over a specified time period. This is fundamentally different from prevalence as it measures new occurrences, not existing cases. You will extract these datapoints using a structured approach with the available tools.

        ## TOOL USAGE INSTRUCTIONS

        ### 1. REASONING TOOLS
        Use the ReasoningTools throughout your analysis:

        **think()**: Use this to:
        - Plan your approach before reading the article
        - Break down complex sections of the article
        - Consider multiple interpretations of data
        - Reflect on extracted information before recording
        - Distinguish between incidence rates and other epidemiological measures

        **analyze()**: Use this to:
        - Examine statistical data and methodology sections
        - Evaluate data quality and reliability
        - Compare different incidence estimates within the same study
        - Assess demographic stratifications and subgroup analyses
        - Calculate person-years when not explicitly provided

        ### 2. DATA EXTRACTION WORKFLOW

        **STEP 1: Initial Analysis**
        - Use think() to plan your extraction strategy
        - Use analyze() to understand the study design, population, and methodology
        - Identify relevant sections (Results, Tables, Figures, Discussion)
        - Focus on longitudinal studies, cohort studies, and surveillance data

        **STEP 2: Systematic Data Identification**
        For each potential datapoint, use analyze() to evaluate:
        - Is this truly incidence rate (new cases per person-time)?
        - Are the cases incident (new) rather than prevalent (existing)?
        - Is the measurement expressed per 100,000 person-years?
        - Are the numerator (new cases) and denominator (person-years) clearly defined?
        - What population demographics are specified?
        - Is the time period clearly defined?

        **STEP 3: Data Recording**
        Use record_incidence_rate_datapoints() for each valid datapoint with:
        - Complete IncidenceRateRecord with all available fields
        - Confidence value between 0.0-1.0 based on data quality
        - Higher confidence (0.8-1.0) for: clear methodology, large person-years, well-defined populations
        - Lower confidence (0.5-0.7) for: unclear definitions, small person-years, limited demographic data

        **STEP 4: Final Database Save**
        Use save_incidence_rate_datapoints() once at the end to persist all extracted data.

        ### 3. INCIDENCE RATE RECORD STRUCTURE

        Extract the following fields for each datapoint:

        **Required Fields:**
        - condition: Disease/condition name (should match the target disease)
        - incidence_rate: Rate per 100,000 person-years (calculated as (n_cases / person_years) * 100,000)
        - start_year: Beginning year of the observation period
        - end_year: Ending year of the observation period

        **Optional but Important Fields:**
        - ci_lower, ci_upper: 95% confidence intervals per 100,000 person-years
        - n_cases: Number of incident (new) cases during the period
        - person_years: Total person-time at risk in years
        - country: ISO code (preferred) or country name
        - age_group: Format as 'all', 'X-Y', '>=X', or '<X'
        - gender: 'male', 'female', 'both', 'other', or 'unknown'
        - ethnicity: Specific ethnicity or 'all'/'unknown'

        ### 4. DATA QUALITY ASSESSMENT

        **High Confidence (0.8-1.0):**
        - Clear incidence rate definition and calculation
        - Large person-years of follow-up (>10,000 person-years)
        - Well-defined observation period
        - Complete demographic information
        - Peer-reviewed source with clear methodology
        - Clear distinction between incident and prevalent cases

        **Medium Confidence (0.6-0.7):**
        - Generally clear but some ambiguity in definitions
        - Moderate person-years (1,000-10,000)
        - Some missing demographic details
        - Limited methodological description
        - Some uncertainty about case definitions

        **Low Confidence (<0.6):**
        - Unclear incidence definition
        - Small person-years (<1,000)
        - Significant missing information
        - Questionable methodology
        - Possible confusion with prevalence or other measures

        ### 5. EXTRACTION GUIDELINES

        **DO Extract:**
        - Cohort study incidence rates
        - Surveillance-based incidence rates
        - Registry-based incidence rates
        - Prospective study incidence rates
        - Subgroup analyses by age, gender, ethnicity, geography
        - Age-standardized incidence rates
        - Crude incidence rates

        **DON'T Extract:**
        - Prevalence estimates (existing cases)
        - Case-fatality rates
        - Mortality rates (unless specifically for incident cases)
        - Attack rates from outbreak investigations
        - Cumulative incidence expressed as proportions/percentages
        - Incidence rates without clear person-time denominators

        **Multiple Datapoints:**
        - Extract separate records for different demographic subgroups
        - Extract separate records for different time periods
        - Extract separate records for different geographic regions
        - Extract separate records for age-standardized vs crude rates

        ### 6. SYSTEMATIC APPROACH

        1. **Initialize**: Use think() to outline your extraction plan
        2. **Scan**: Use analyze() to identify all potential incidence data in tables, figures, and text
        3. **Evaluate**: For each potential datapoint, use think() to assess if it meets incidence rate criteria
        4. **Calculate**: Use analyze() to verify or calculate rates per 100,000 person-years when needed
        5. **Extract**: Use analyze() to carefully extract all relevant values and metadata
        6. **Record**: Use record_incidence_rate_datapoints() with appropriate confidence scores
        7. **Validate**: Use think() to review all extracted datapoints for completeness and accuracy
        8. **Save**: Use save_incidence_rate_datapoints() to persist all data
        9. **Summarize**: State the total number of incidence rate datapoints extracted and successfully saved to the database.

        ### 7. COMMON PITFALLS TO AVOID

        - Don't extract prevalence as incidence
        - Don't extract cumulative incidence (risk) as incidence rate
        - Don't record the same datapoint multiple times
        - Don't extract incidence from studies without clear person-time denominators
        - Don't assume demographic characteristics if not explicitly stated
        - Don't extract incidence estimates that are clearly projections or estimates for future years
        - Don't confuse attack rates or case-fatality rates with incidence rates

        ### 8. CALCULATION GUIDELINES

        **Standard Formula**: Incidence Rate = (Number of new cases / Person-years at risk) × 100,000

        **Person-Years Calculation**: 
        - If not provided, may be approximated as: (Average population) × (Years of follow-up)
        - For cohort studies: Sum of individual follow-up times
        - Always express final rate per 100,000 person-years for consistency

        **Rate Conversion**:
        - If rate given per 1,000 person-years: multiply by 100
        - If rate given per 10,000 person-years: multiply by 10
        - If rate given per 1,000,000 person-years: divide by 10

        ## FINAL REMINDERS

        - Use reasoning tools extensively to ensure accuracy
        - Extract only incidence rates (not other epidemiological measures)
        - Record each unique datapoint separately
        - Assign appropriate confidence scores
        - Save all datapoints once at the end
        - Focus on the target disease but be aware of related conditions
        - Prioritize recent, high-quality studies with large person-years of follow-up
        - Always verify that extracted data represents new cases, not existing ones
        """
    )

def extract_incidence_rate_from_article(disease_name:str, pmcid: str, pmid:str) -> Dict[str,Any]:
    """
    Extract incidence rate datapoints from an full article markdown file.
    """

    pmcid_number = pmcid.split("/")[-1]
    with open(f"{project_root}/data/publications/{pmcid_number}.md", "r") as f:
        fulltext_article = f.read()

    tools = []
    if incidence_rate_config["use_reasoning"]:
        tools.append(ReasoningTools(enable_think=True, enable_analyze=True))
    tools.append(record_incidence_rate_datapoints)
    tools.append(save_incidence_rate_datapoints)

    agent = Agent(
        name="Incidence Rate Extraction Agent",
        model=OpenAIChat(id=incidence_rate_config["model_id"]),
        description="Analyze, record and save incidence rate datapoints into epidemiology research database from an full article markdown file of epidemiology article.",
        tools=tools,
        instructions=get_incidence_rate_extraction_instructions(),
        delay_between_retries=random.randint(1,3),
        exponential_backoff=True,
        debug_mode=incidence_rate_config["debug_mode"],
        telemetry=False
    )
    
    status_object = {
        "disease": disease_name,
        "PMID": pmid,
        "PMCID": pmcid,
        "task": "incidence_rate_extraction",
        "status": "RUNNING",
        "extracted_datapoints": 0,
        "summary": ""
    }
    
    # Initialize database connection for updating extraction status
    db_connection = DatabaseConnection()
    extraction_manager = None
    try:
        db_connection.connect()
        extraction_manager = ExtractionManager(db_connection)
        
        # Update status to "running" when starting extraction
        extraction_manager.update_extraction_status(
            pmid=pmid,
            pmcid=pmcid,
            task_type="incidence_rate_extraction",
            status="running"
        )
        status_object['status'] = "RUNNING"
        
    except Exception as e:
        logger.warning(f"Could not update extraction status to running: {e}")
    
    try:
        current_session = {"pmid": pmid, "pmcid": pmcid, "disease_name": disease_name, "incidence_rate_datapoints":[]}
        response = agent.run(dedent(f"""
            Read the attached fulltext epidemiology article about {disease_name}. Think step-by-step and analyze the article for carefully extracting incidence rate datapoints.

            FULLTEXT ARTICLE:
            {fulltext_article}
            """),
            session_state=current_session
        )
        if isinstance(response.content, str):
            status_object['status'] = "SUCCESS"
            status_object['extracted_datapoints'] = len(current_session['incidence_rate_datapoints'])
            status_object['summary'] = response.content
        else:
            status_object['status'] = "FAILED"
            status_object['extracted_datapoints'] = 0
            status_object['summary'] = f"FAILED: Incidence Rate Extraction Error Type: {type(response)}"
    except Exception as e:
        status_object['status'] = "FAILED"
        status_object['extracted_datapoints'] = 0
        status_object['summary'] = f"FAILED: Exception during incidence rate extraction from article - {e}"
    finally:
        # Update dim_extraction table with final results
        if extraction_manager:
            try:
                extraction_manager.update_extraction_from_response(status_object)
                logger.debug(f"Updated dim_extraction table for PMID {pmid}")
            except Exception as e:
                logger.warning(f"Failed to update dim_extraction table: {e}")
        
        # Close database connection
        if db_connection and db_connection.engine:
            db_connection.close()
    
    return status_object
    

if __name__ == "__main__":
    # example usage
    pmid = "PMC4068111"
    pmcid = "PMC4068111"
    disease_name = "Diabetes Mellitus"
    extract_incidence_rate_from_article(disease_name, pmcid, pmid)
