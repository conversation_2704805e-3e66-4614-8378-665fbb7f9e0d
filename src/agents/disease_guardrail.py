from dotenv import load_dotenv

load_dotenv()

from textwrap import dedent
from typing import List, Optional, Dict, Any
from agno.agent import Agent
from agno.models.openai import OpenAIChat
import json
import os
from pydantic import BaseModel
import random


class DiseaseValidation(BaseModel):
    is_disease: bool
    explanation: str
    suggested_corrections: Optional[List[str]] = None


class DiseaseGuardrailAgent:
    """
    Agent for validating disease names.
    """

    def __init__(self) -> None:
        """
        Initialize the Disease Guardrail Agent
        """
        path = os.path.dirname(os.path.abspath(__file__))
        with open(os.path.join(path, "..", "config.json")) as f:
            config = json.load(f)["DiseaseGuardrailAgent"]

        self.validation_agent = Agent(
            model=OpenAIChat(id=config["model_id"]),
            instructions=self._get_validation_instructions(),
            retries=config["retries"],
            exponential_backoff=True,
            delay_between_retries=random.randint(1,3),
            output_schema=DiseaseValidation,
            debug_mode=config["debug_mode"],
            telemetry=False,
        )

    def _get_validation_instructions(self) -> str:
        """Get detailed instructions for disease validation."""
        return dedent(
            """
            You are a medical terminology expert. Your task is to validate whether a given term is actually a disease, medical condition, or medical indication.

            Analyze the provided term and determine:

            1. IS_DISEASE: Whether the term represents a recognized disease, medical condition, 
               syndrome, disorder, or medical indication (true/false)
            2. EXPLANATION: A brief explanation of why the term is or is not considered a disease
            3. SUGGESTED_CORRECTIONS: If the term is not a disease but could be corrected 
               to a valid disease name, provide suggestions

            Consider as valid diseases:
            - Recognized medical diseases and conditions
            - Syndromes and disorders
            - Medical indications for treatment
            - Genetic conditions
            - Mental health conditions
            - Infectious diseases
            - Chronic conditions

            Do NOT consider as diseases:
            - General symptoms (unless they represent a specific condition)
            - Medical procedures or treatments
            - Anatomical parts
            - General health concepts
            - Non-medical terms
            """
        )

    def validate_disease(self, term: str) -> Dict[str, Any]:
        """
        Validate whether the given term is actually a disease name.

        Args:
            term: Term to validate as a disease name

        Returns:
            Dictionary containing validation results
        """
        prompt = (
            f'Validate whether the following term is a recognized disease, medical condition, or medical indication: "{term}".'
        )

        try:
            response = self.validation_agent.run(prompt)

            if isinstance(response.content, DiseaseValidation):
                return response.content.model_dump()
            else:
                print(f"Validation Error: {response}\n\n type:{type(response)}")
                return {
                    "is_disease": False,
                    "explanation": "Error during validation",
                    "suggested_corrections": None,
                }
        except Exception as e:
            print(f"Exception during validation: {e}")
            return {
                "is_disease": False,
                "explanation": "Error during validation",
                "suggested_corrections": None,
            }


def validate_disease_name(term: str) -> Dict[str, Any]:
    """
    Convenience function to validate a term as a disease name.

    Args:
        term: Term to validate

    Returns:
        Dictionary containing validation results
    """
    agent = DiseaseGuardrailAgent()
    return agent.validate_disease(term)


if __name__ == "__main__":
    print("Testing Disease Guardrail Agent...")

    # Test with a valid disease
    print("\n=== Testing with 'Ulcerative Colitis' ===")
    result = validate_disease_name("Ulcerative Colitis")

    print(f"Is Disease: {result['is_disease']}")
    print(f"Explanation: {result['explanation']}")
    if result.get("suggested_corrections"):
        print("Suggested Corrections:")
        print(json.dumps(result["suggested_corrections"], indent=4, ensure_ascii=False))

    # Test with a non-disease term
    print("\n\n=== Testing with 'headache' ===")
    result2 = validate_disease_name("headache")

    print(f"Is Disease: {result2['is_disease']}")
    print(f"Explanation: {result2['explanation']}")
    if result2.get("suggested_corrections"):
        print("Suggested Corrections:")
        print(json.dumps(result2["suggested_corrections"], indent=4, ensure_ascii=False))