from dotenv import load_dotenv
load_dotenv()

import random
from textwrap import dedent
from typing import Dict, List, Generator
from agno.agent import Agent
from agno.models.openai import OpenAIChat
import json
import os
from pydantic import BaseModel
from agno.tools.reasoning import ReasoningTools
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

from pydantic import BaseModel


class DiseaseEpidemiologyScreeningResult(BaseModel):
    """Result model for disease-specific epidemiology abstract screening.

    Attributes:
        is_relevant (bool): Whether the title and abstract represents a simple epidemiology study with clear prevalence and/or incidence data for the given disease.
    """

    is_relevant: bool



class DiseaseAbstractScreeningAgent:
    """
    Agent for screening scientific abstracts to determine if they are disease epidemiology studies
    for a specific disease.
    """

    def __init__(self) -> None:
        """
        Initialize the Disease Abstract Screening Agent.
        """
        path = os.path.dirname(os.path.abspath(__file__))
        with open(os.path.join(path, "..", "config.json")) as f:
            config = json.load(f)["DiseaseAbstractScreeningAgent"]

        self.screening_agent = Agent(
            model=OpenAIChat(id=config["model_id"]),
            instructions=self._get_screening_instructions(),
            tools=[ReasoningTools()] if config["use_reasoning"] else None,
            retries=config["retries"],
            delay_between_retries=random.randint(1,3),
            exponential_backoff=True,
            output_schema=DiseaseEpidemiologyScreeningResult,
            debug_mode=config["debug_mode"],
            telemetry=False
        )

    def _get_screening_instructions(self) -> str:
        """Get detailed instructions for disease-specific abstract screening aligned with OpenAlex query preferences."""
        return dedent(
            """
            You are an expert epidemiologist and medical researcher. Your task is to analyze scientific titles and abstracts to evaluate whether they represent epidemiology studies for the specified disease that report incidence and/or prevalence data using preferred study designs.

            IS_RELEVANT: Does the title and abstract represent an epidemiology study for the specified disease that reports incidence and/or prevalence data using preferred study designs? (true/false)

            A study qualifies as RELEVANT ONLY if it meets ALL of the following criteria:
            1. The specified disease is the PRIMARY focus of the study
            2. The study reports epidemiological outcomes: incidence, incidence rate, incidence density, cumulative incidence, prevalence, prevalence rate, point prevalence, or period prevalence
            3. The study uses one of the PREFERRED study designs listed below
            4. The study is NOT one of the EXCLUDED study types

            **STRONGLY PREFERRED STUDY DESIGNS (High Priority for INCLUSION):**
            - Cross-sectional studies or cross sectional surveys
            - Cohort studies (prospective or retrospective) reporting incidence
            - Population-based studies and surveys
            - Registry-based studies using disease registries
            - Surveillance studies and surveillance systems
            - Government health surveys (NHANES, BRFSS, etc.)
            - National or regional disease registries
            - Epidemiological surveillance reports

            **PREFERRED OUTCOME MEASURES (Must Report At Least One):**
            - Incidence (any type: incidence, incidence rate, incidence density, cumulative incidence)
            - Prevalence (any type: prevalence, prevalence rate, point prevalence, period prevalence)
            - Clear epidemiological statistics with confidence intervals
            - Population-level disease burden estimates

            **AUTOMATICALLY EXCLUDE (Based on OpenAlex NOT criteria):**
            - Systematic reviews and scoping reviews
            - Meta-analyses and meta-analysis studies
            - Mendelian randomization studies
            - In vitro studies and laboratory research
            - Animal studies (murine, mouse, rat, animal models)
            - Clinical trials focusing on treatment efficacy
            - Case reports and case series
            - Studies focusing primarily on risk factors, biomarkers, or mechanisms
            - Studies where disease is secondary to other conditions

            **ADDITIONAL EXCLUSION CRITERIA:**
            - Modeling or prediction studies without real data
            - Studies using complex statistical modeling as primary methodology
            - Hospital-based studies with unclear population denominators
            - Studies combining multiple unrelated conditions
            - Genetic association studies
            - Intervention or treatment evaluation studies
            - Diagnostic accuracy studies

            **QUALITY INDICATORS for INCLUSION:**
            - Clear study population with defined denominators
            - Specific geographic area and time period
            - Standard case definitions or diagnostic criteria
            - Straightforward epidemiological methods
            - Population-based sampling or complete enumeration
            - Government or institutional surveillance data
            - Clear reporting of rates, percentages, or counts with denominators

            **TITLE AND ABSTRACT ANALYSIS:**
            - Consider BOTH title and abstract when making determinations
            - Title should clearly indicate epidemiological focus on the specified disease
            - Abstract should describe appropriate study design and outcome measures
            - Look for keywords matching preferred designs and outcome measures
            - Verify the specified disease is the main focus, not just mentioned

            CRITICAL: Prioritize studies that provide clear, interpretable incidence or prevalence data for the specified disease using the preferred study designs. The methodology should be straightforward epidemiological approaches that align with surveillance and population health research goals.

            Focus exclusively on the specified disease when making your determination.
            """
        )

    def screen_abstract(
        self, disease: str, title: str, abstract: str
    ) -> Dict[str, bool]:
        """
        Screen a title and abstract to determine if it's an epidemiology study for a specific disease.

        Args:
            disease (str): The disease name to check for in the title and abstract.
            title (str): The scientific article title to analyze.
            abstract (str): The scientific abstract text to analyze.

        Returns:
            Dict[str, bool]: Dictionary containing screening result:
                - is_relevant
        """
        if not abstract or not abstract.strip() or not disease or not disease.strip():
            return {
                "is_relevant": False,
            }

        # Handle missing title gracefully
        title_text = title.strip() if title else "No title provided"

        prompt = f"""
        Analyze the following scientific title and abstract to determine if it represents an epidemiology study for the specified disease that reports incidence and/or prevalence data using preferred study designs.

        DISEASE NAME: 
        {disease}

        TITLE:
        {title_text}

        ABSTRACT:
        {abstract}
        """

        try:
            response = self.screening_agent.run(prompt, disease=disease)
            if isinstance(response.content, DiseaseEpidemiologyScreeningResult):
                return response.content.model_dump()
            else:
                print(f"Screening Error: {response}\n\n type:{type(response)}")
                return {
                    "is_relevant": False,
                }
        except Exception as e:
            print(f"Exception during screening: {e}")
            return {
                "is_relevant": False,
            }


def screen_abstract_for_disease_epidemiology(
    disease: str, title: str, abstract: str
) -> Dict[str, bool]:
    """
    Convenience function to screen a single title and abstract for a specific disease.

    Args:
        disease (str): The disease name to check for in the title and abstract.
        title (str): The article title to analyze.
        abstract (str): The abstract text to analyze.

    Returns:
        Dict[str, bool]: Dictionary containing screening result.
    """
    agent = DiseaseAbstractScreeningAgent()
    return agent.screen_abstract(disease, title, abstract)


def screen_abstracts_for_disease_epidemiology(
    disease: str, titles: List[str], abstracts: List[str], max_workers: int = 5
) -> List[Dict[str, bool]]:
    """
    Convenience function to screen multiple titles and abstracts for a specific disease.

    Args:
        disease (str): The disease name to check for in the titles and abstracts.
        titles (List[str]): The article titles to analyze.
        abstracts (List[str]): The abstract texts to analyze.
        max_workers (int): Maximum number of worker threads for parallel processing.

    Returns:
        List[Dict[str, bool]]: List of screening results for each title/abstract pair.
    """

    if len(titles) != len(abstracts):
        raise ValueError("Number of titles must match number of abstracts")
        
    results = [{}] * len(abstracts)  # Pre-allocate list to maintain order

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks with their indices
        future_to_index = {
            executor.submit(screen_abstract_for_disease_epidemiology, disease, title, abstract): i
            for i, (title, abstract) in enumerate(zip(titles, abstracts))
        }

        # Collect results as they complete
        for future in as_completed(future_to_index):
            index = future_to_index[future]
            result = future.result()
            results[index] = result
    return results

def screen_abstracts_for_disease_epidemiology_generator(
    ids: List[str], disease: str, titles: List[str], abstracts: List[str], max_workers: int = 5
) -> Generator[Dict[str, bool], None, None]:
    """
    Convenience function to screen multiple titles and abstracts for a specific disease.

    Args:
        ids (List[str]): The article IDs to analyze.
        disease (str): The disease name to check for in the titles and abstracts.
        titles (List[str]): The article titles to analyze.
        abstracts (List[str]): The abstract texts to analyze.
        max_workers (int): Maximum number of worker threads for parallel processing.

    Returns:
        Generator[Dict[str, bool], None, None]: Generator of screening results for each title/abstract pair.
    """

    if len(titles) != len(abstracts):
        raise ValueError("Number of titles must match number of abstracts")
        
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks with their titles and abstracts
        future_to_index = {
            executor.submit(screen_abstract_for_disease_epidemiology, disease, title, abstract): (id, title, abstract)
            for id, title, abstract in zip(ids, titles, abstracts)
        }

        # Collect results as they complete
        for future in as_completed(future_to_index):
            id, title, abstract = future_to_index[future]
            result = future.result()
            final_result = {
                "id": id,
                "title": title, 
                "abstract": abstract,
                "is_relevant": result["is_relevant"]
            }
            yield final_result

if __name__ == "__main__":
    print("Testing Disease-Specific Abstract Screening Agent...")

    # Test with a sample epidemiology abstract for Type 2 diabetes
    disease = "Type 2 diabetes mellitus"
    epidemiology_title = "Prevalence and incidence of Type 2 diabetes mellitus in urban adults: a population-based cross-sectional study, 2018-2022"
    epidemiology_abstract = """
    Objective: To estimate the prevalence and incidence of Type 2 diabetes mellitus in the adult population of Urban City from 2018-2022. Methods: We conducted a population-based cross-sectional study using electronic health records from 50 primary care clinics. The study included 125,000 adults aged 18-65 years. Diabetes prevalence was calculated for each year, and incidence rates were determined for new diagnoses. Results: The overall prevalence of Type 2 diabetes increased from 8.2% (95% CI: 7.9-8.5%) in 2018 to 9.7% (95% CI: 9.4-10.0%) in 2022. The annual incidence rate was 12.5 per 1,000 person-years (95% CI: 11.8-13.2). Higher incidence was observed in males and individuals aged 45-65 years. Conclusion: Type 2 diabetes prevalence and incidence continue to rise in this urban population, highlighting the need for enhanced prevention strategies.
    """

    print("\n=== Testing with Epidemiology Abstract for Type 2 diabetes mellitus ===")
    result = screen_abstract_for_disease_epidemiology(disease, epidemiology_title, epidemiology_abstract)

    print(f"Is Relevant Study: {result['is_relevant']}")

    # Test with a non-epidemiology abstract for rheumatoid arthritis
    disease2 = "rheumatoid arthritis"
    clinical_trial_title = "Efficacy and safety of Drug X versus placebo in rheumatoid arthritis: a randomized controlled trial"
    clinical_trial_abstract = """
    Background: This randomized controlled trial evaluated the efficacy of Drug X versus placebo 
    in treating patients with rheumatoid arthritis. Methods: 200 patients were randomized to receive 
    either Drug X (n=100) or placebo (n=100) for 12 weeks. Primary outcome was reduction in 
    Disease Activity Score. Results: Drug X group showed significantly greater improvement in 
    disease activity compared to placebo (p<0.001). Side effects were mild and manageable. 
    Conclusion: Drug X is effective and safe for treating rheumatoid arthritis.
    """

    print("\n\n=== Testing with Clinical Trial Abstract for rheumatoid arthritis ===")
    result2 = screen_abstract_for_disease_epidemiology(disease2, clinical_trial_title, clinical_trial_abstract)

    print(f"Is Relevant Study: {result2['is_relevant']}")

    # Test with multiple abstracts using parallel processing
    disease3 = "hypertension"
    multiple_titles = [
        "Prevalence of hypertension in rural Bangladesh: a cross-sectional population-based survey",
        "Effectiveness of a new antihypertensive medication in resistant hypertension: a randomized trial",
        "Ten-year incidence of hypertension in middle-aged adults: a prospective cohort study"
    ]
    multiple_abstracts = [
        """
        Objective: To determine the prevalence of hypertension in adults aged 25-64 years in rural Bangladesh. 
        Methods: A cross-sectional population-based survey was conducted among 2,500 adults. Blood pressure 
        measurements were taken using standardized protocols. Results: The overall prevalence of hypertension 
        was 28.4% (95% CI: 26.7-30.1%). Prevalence increased with age and was higher in males. 
        Conclusion: Hypertension prevalence is substantial in rural Bangladesh.
        """,
        """
        Background: This study evaluated the effectiveness of a new antihypertensive medication in 
        patients with resistant hypertension. Methods: 150 patients were enrolled in a double-blind 
        randomized trial. Primary endpoint was reduction in systolic blood pressure after 12 weeks. 
        Results: The new medication showed significant blood pressure reduction compared to placebo 
        (p<0.001). Conclusion: The new medication is effective for treating resistant hypertension.
        """,
        """
        Objective: To assess the 10-year incidence of hypertension in a cohort of middle-aged adults. 
        Methods: A prospective cohort study followed 5,000 normotensive adults aged 40-60 years from 
        2010-2020. Annual health screenings documented new hypertension diagnoses. Results: The cumulative 
        incidence of hypertension was 35.2% over 10 years. Incidence rate was 42.1 per 1,000 person-years. 
        Conclusion: Hypertension incidence remains high in this population.
        """
    ]

    print("\n\n=== Testing with Multiple Abstracts for hypertension (Parallel Processing) ===")
    results = screen_abstracts_for_disease_epidemiology(disease3, multiple_titles, multiple_abstracts, max_workers=3)

    for i, result in enumerate(results):
        print(f"\nAbstract {i+1}:")
        print(f"  Title: {multiple_titles[i]}")
        print(f"  Is Relevant Study: {result['is_relevant']}")
