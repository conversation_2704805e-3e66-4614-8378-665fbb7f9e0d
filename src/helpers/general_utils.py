from typing import List, Literal
import logging
import os
from datetime import datetime

def add_upper_and_lowercase(terms: List[str]) -> List[str]:
    """
    Add the upper and lowercase versions of a term to a list.
    """
    updated_terms = set()
    for term in terms:
        updated_terms.add(term)
        updated_terms.add(term.lower())
        updated_terms.add(term.upper())
    return list(updated_terms)


def setup_logging(
        logs_dir: str = "logs", 
        file_name: str = "europepmc",
        logging_level: Literal['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'] = 'WARNING'
    ) -> logging.Logger:
    """
    Configure logging with simplified setup.
    """
    os.makedirs(logs_dir, exist_ok=True)
    
    level = getattr(logging, logging_level)
    
    # Single formatter for both console and file
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    
    # File handler
    log_file = f'{logs_dir}/{file_name}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)
    
    # Configure logger
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)
    logger.handlers = []
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
    
    return logger