from typing import Optional, Literal
from pydantic import BaseModel, Field

# -----------------------------
# 1) Point Prevalence (at a single year)
# -----------------------------
class PointPrevalenceRecord(BaseModel):
    condition: str = Field(..., description="Short free-form name for the disease/condition. e.g., 'Type 2 diabetes'.")
    point_prevalence_percent: float = Field(..., ge=0, le=100,
        description=("Point prevalence as PERCENT in range [0,100] at the specified year. Computed as (n_cases / n_population) * 100. Report upto 1 decimal place precision.")
    )
    ci_lower_percent: Optional[float] = Field(None, ge=0, le=100, description="Lower 95% CI for point prevalence, in percent.")
    ci_upper_percent: Optional[float] = Field(None, ge=0, le=100, description="Upper 95% CI for point prevalence, in percent.")
    n_cases: Optional[int] = Field(None, ge=0, description="Number of people with the condition in the specified year.")
    n_population: Optional[int] = Field(None, ge=1, description="Number of people assessed in the specified year.")
    year: int = Field(..., ge=1800, le=2100, description="Calendar year (YYYY) of measurement (e.g., 2021).")

    # Demographics / location (flat)
    country: Optional[str] = Field(
        None,
        description=("Country (single field). Prefer ISO 3166-1 alpha-2 code like 'IN'. If unavailable, use full country name.")
    )
    age_group: Optional[str] = Field(
        None,
        description=("Single age attribute. Use one of: 'NA', 'X-Y' (inclusive, years), '>=X', '<X'. Examples: 'NA', '18-49', '>=65', '<5'.")
    )
    gender: Optional[Literal["male", "female", "both", "unknown"]] = Field("both", description="Gender category for this estimate.")
    ethnicity: Optional[str] = Field(
        None,
        description=("Single ethnicity label as reported. Use 'NA' if not stratified; 'unknown' if not reported. If the paper uses a combined stratum, store the literal combined label as one string (e.g., 'Black and Hispanic combined').")
    )

# -----------------------------
# 2) Period Prevalence (over a year range)
# -----------------------------
class PeriodPrevalenceRecord(BaseModel):
    condition: str = Field(..., description="Short free-form name for the disease/condition. e.g., 'Type 2 diabetes'.")
    period_prevalence_percent: float = Field(
        ..., ge=0, le=100,
        description=("Period prevalence as PERCENT in [0,100] over the specified year range. Numerator counts unique persons who had the condition at any time in the range.")
    )
    ci_lower_percent: Optional[float] = Field(None, ge=0, le=100, description="Lower 95% CI for period prevalence, in percent.")
    ci_upper_percent: Optional[float] = Field(None, ge=0, le=100, description="Upper 95% CI for period prevalence, in percent.")
    n_cases: Optional[int] = Field(None, ge=0, description="Unique persons with the condition during the year range.")
    n_population: Optional[int] = Field(None, ge=1, description="Population denominator relevant to the year range.")
    start_year: int = Field(..., ge=1800, le=2100, description="Start year (YYYY) of measurement.")
    end_year: int = Field(..., ge=1800, le=2100, description="End year (YYYY) of measurement.")

    country: Optional[str] = Field(
        None,
        description=("Country (single field). Prefer ISO 3166-1 alpha-2 code like 'IN'. If unavailable, use full country name.")
    )
    age_group: Optional[str] = Field(None, description="Single age attribute. Use one of: 'NA', 'X-Y' (inclusive, years), '>=X', '<X'. Examples: 'NA', '18-49', '>=65', '<5'.")
    gender: Optional[Literal["male", "female", "both", "unknown"]] = Field("both", description="Gender category for this estimate.")
    ethnicity: Optional[str] = Field(
        None,
        description=("Single ethnicity label as reported. Use 'NA' if not stratified; 'unknown' if not reported. If combined stratum, store the literal combined label as one string.")
    )

# -----------------------------
# 3) Incidence Rate (per person-time, year range)
# -----------------------------
class IncidenceRateRecord(BaseModel):
    condition: str = Field(..., description="Short free-form name for the disease/condition. e.g., 'Type 2 diabetes'.")
    incidence_rate: float = Field(
        ..., ge=0,
        description=("Incidence RATE as the number of new cases per 100,000 PERSON-YEARS over the specified year range. Compute as (n_cases / person_years) * 100000. This is a rate (1/time), not a proportion.")
    )
    ci_lower: Optional[float] = Field(None, ge=0, description="Lower 95% CI per 100,000 person-years.")
    ci_upper: Optional[float] = Field(None, ge=0, description="Upper 95% CI per 100,000 person-years.")
    n_cases: Optional[int] = Field(None, ge=0, description="Number of incident cases during the year range.")
    person_years: Optional[float] = Field(None, ge=0, description="Total person-time at risk in YEARS over the year range.")
    start_year: int = Field(..., ge=1800, le=2100, description="Start year (YYYY) of measurement.")
    end_year: int = Field(..., ge=1800, le=2100, description="End year (YYYY) of measurement.")

    country: Optional[str] = Field(
        None,
        description=("Country (single field). Prefer ISO 3166-1 alpha-2 code like 'IN'. If unavailable, use full country name.")
    )
    age_group: Optional[str] = Field(None, description="Single age attribute. Use one of: 'NA', 'X-Y' (inclusive, years), '>=X', '<X'. Examples: 'NA', '18-49', '>=65', '<5'.")
    gender: Optional[Literal["male", "female", "both", "other", "unknown"]] = Field("both", description="Gender category for this estimate.")
    ethnicity: Optional[str] = Field(
        None,
        description=("Single ethnicity label as reported. Use 'NA' if not stratified; 'unknown' if not reported. If combined stratum, store the literal combined label as one string.")
    )

# -----------------------------
# 4) Cumulative Incidence (Risk over a year range)
# -----------------------------
class CumulativeIncidenceRecord(BaseModel):
    condition: str = Field(..., description="Disease/condition (free text).")
    cumulative_incidence_percent: float = Field(
        ..., ge=0, le=100,
        description=("Cumulative incidence (RISK) as a PERCENT in [0,100] over the specified year range. Compute as (n_new_cases / n_at_risk_start) * 100. This is a proportion over time (not per PY).")
    )
    ci_lower_percent: Optional[float] = Field(None, ge=0, le=100, description="Lower 95% CI for cumulative incidence, in percent.")
    ci_upper_percent: Optional[float] = Field(None, ge=0, le=100, description="Upper 95% CI for cumulative incidence, in percent.")
    n_new_cases: Optional[int] = Field(None, ge=0, description="Number of incident cases during the year range.")
    n_at_risk_start: Optional[int] = Field(None, ge=0, description="Number of disease-free individuals at the START of the year range.")
    start_year: int = Field(..., ge=1800, le=2100, description="Start year (YYYY) of measurement.")
    end_year: int = Field(..., ge=1800, le=2100, description="End year (YYYY) of measurement.")

    country: Optional[str] = Field(
        None,
        description=("Country (single field). Prefer ISO 3166-1 alpha-2 code like 'IN'. If unavailable, use full country name.")
    )
    age_group: Optional[str] = Field(None, description="Single age attribute. Use one of: 'NA', 'X-Y' (inclusive, years), '>=X', '<X'. Examples: 'NA', '18-49', '>=65', '<5'.")
    gender: Optional[Literal["male", "female", "both", "other", "unknown"]] = Field("both", description="Gender category for this estimate.")
    ethnicity: Optional[str] = Field(
        None,
        description=("Single ethnicity label as reported. Use 'NA' if not stratified; 'unknown' if not reported. If combined stratum, store the literal combined label as one string.")
    )

# 5) Summary
class ExtractionSummary(BaseModel):
    identified_datapoints: int = Field(..., ge=0, description="Number of datapoints identified in the article.")
    extracted_datapoints: int = Field(..., ge=0, description="Number of datapoints successfully extracted and saved into database from the article.")