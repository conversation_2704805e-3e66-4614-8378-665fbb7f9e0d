
"""
Markdown operations for processing research articles.
Provides utilities to extract structure and content from markdown research papers.
"""

# Add project root to Python path for imports
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import os
import json
import re
from typing import Dict, List, Optional, Tuple
from pathlib import Path


def get_article_structure(pmcid: str) -> str:
    """
    Extract and return the hierarchical structure (sections, subsections, sub-subsections) from a markdown file.
    
    Args:
        pmcid: The PMC ID of the article
        
    Returns:
        JSON string with section hierarchy where keys are main sections and values are nested dictionaries
        containing subsections and their sub-subsections
        
    Raises:
        FileNotFoundError: If the markdown file doesn't exist
    """
    filepath = Path(f"{project_root}/data/publications/{pmcid}.md")
    if not filepath.exists():
        raise FileNotFoundError(f"Markdown file not found for PMCID: {pmcid}")
    
    structure = {}
    current_section = None
    current_subsection = None
    
    with open(filepath, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            
            # Match headers (# ## ### etc.)
            header_match = re.match(r'^(#{1,6})\s+(.+)$', line)
            if header_match:
                level = len(header_match.group(1))
                title = header_match.group(2)
                
                if level == 1:
                    # Main section
                    current_section = title
                    current_subsection = None
                    structure[current_section] = {}
                elif level == 2 and current_section:
                    # Subsection
                    current_subsection = title
                    structure[current_section][current_subsection] = []
                elif level == 3 and current_section and current_subsection:
                    # Sub-subsection
                    structure[current_section][current_subsection].append(title)
    
    return json.dumps(structure, indent=2, ensure_ascii=False)


def get_article_section_content(pmcid: str, section_name: str) -> Optional[str]:
    """
    Retrieve content from a specific section by name from markdown article
    
    Args:
        pmcid: The PMC ID of the article
        section_name: Name of the section to extract (case-insensitive)
        
    Returns:
        Content of the section as string, or None if section not found
        
    Raises:
        FileNotFoundError: If the markdown file doesn't exist
    """
    filepath = Path(f"{project_root}/data/publications/{pmcid}.md")
    if not filepath.exists():
        raise FileNotFoundError(f"Markdown file not found for PMCID: {pmcid}")
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Split content by headers
    sections = re.split(r'^(#{1,6}\s+.+)$', content, flags=re.MULTILINE)
    
    for i in range(1, len(sections), 2):
        if i < len(sections):
            header = sections[i].strip()
            section_content = sections[i + 1].strip() if i + 1 < len(sections) else ""
            
            # Extract section title from header
            header_match = re.match(r'^#{1,6}\s+(.+)$', header)
            if header_match:
                title = header_match.group(1).strip()
                if title.lower() == section_name.lower():
                    return section_content.strip()
    
    return None


def get_full_article_content(pmcid: str) -> str:
    """
    Return the complete article content.
    
    Args:
        pmcid: The PMC ID of the article
        
    Returns:
        Complete content of the markdown file as string
        
    Raises:
        FileNotFoundError: If the markdown file doesn't exist
    """
    filepath = Path(f"{project_root}/data/publications/{pmcid}.md")
    if not filepath.exists():
        raise FileNotFoundError(f"Markdown file not found for PMCID: {pmcid}")
    
    with open(filepath, 'r', encoding='utf-8') as f:
        return f.read()

if __name__ == "__main__":
    num_articles = 1
    example_pmcids = [
        foo.replace(".md", "") 
        for foo in os.listdir(f"{project_root}/data/publications")
    ][:1]
    for pmcid in example_pmcids:
        print("--------------------------------")
        print(f"PMCID: {pmcid}")
        print(get_article_structure(pmcid), "\n\n\n")
        print(get_article_section_content(pmcid, "Results"), "\n\n\n")
        # print(get_full_article_content(pmcid), "\n\n\n")
        print("--------------------------------")