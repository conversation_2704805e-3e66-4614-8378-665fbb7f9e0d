"""
Article management operations for the epidemiology research system.
"""

from typing import List, Dict, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
import pandas as pd
import logging

from .connection import DatabaseConnection
from .models import DimArticles, DimExtraction

logger = logging.getLogger(__name__)


class ArticleManager:
    """
    Manages article operations in the database including CRUD operations
    for the dim_articles table.
    """
    
    def __init__(self, db_connection: DatabaseConnection):
        """
        Initialize ArticleManager with database connection.
        
        Args:
            db_connection: DatabaseConnection instance
        """
        self.db_connection = db_connection
    
    def save_articles_from_openalex(self, publications_df: pd.DataFrame, disease_name: str) -> Dict[str, int]:
        """
        Save articles from OpenAlex search results to the database.
        
        Args:
            publications_df: DataFrame with OpenAlex publication data
                           Must contain columns: pmid (index), pmcid, title, abstract,
                           publication_type, publication_year, pdf_url
            disease_name: Disease name to associate with these articles
        
        Returns:
            Dictionary with statistics: {"inserted": count, "updated": count, "errors": count}
        """
        session = self.db_connection.get_session()
        stats = {"inserted": 0, "updated": 0, "errors": 0}
        
        try:
            for pmid, row in publications_df.iterrows():
                try:
                    # Check if article already exists for this disease and pmcid
                    pmcid_value = row.get('pmcid')
                    if not pmcid_value:
                        # Skip articles without PMCID since it's now required
                        stats["errors"] += 1
                        logger.warning(f"Skipping article with PMID {pmid}: no PMCID provided")
                        continue
                    
                    existing_article = session.query(DimArticles).filter(
                        DimArticles.pmid == str(pmid),
                        DimArticles.disease_name == disease_name,
                        DimArticles.pmcid == str(pmcid_value)
                    ).first()
                    
                    if existing_article:
                        # Update existing article with new data
                        existing_article.title = str(row.get('title', ''))
                        existing_article.abstract = str(row.get('abstract', ''))
                        existing_article.publication_type = row.get('publication_type')
                        existing_article.publication_year = row.get('publication_year')
                        existing_article.pdf_url = row.get('pdf_url')
                        stats["updated"] += 1
                        logger.debug(f"Updated article with PMID: {pmid}, PMCID: {pmcid_value} for disease: {disease_name}")
                    else:
                        # Create new article
                        new_article = DimArticles(
                            pmid=str(pmid),
                            disease_name=disease_name,
                            pmcid=str(pmcid_value),
                            title=row.get('title', ''),
                            abstract=row.get('abstract', ''),
                            publication_type=row.get('publication_type'),
                            publication_year=row.get('publication_year'),
                            pdf_url=row.get('pdf_url')
                        )
                        session.add(new_article)
                        stats["inserted"] += 1
                        logger.debug(f"Inserted new article with PMID: {pmid}, PMCID: {pmcid_value} for disease: {disease_name}")
                        
                except Exception as e:
                    stats["errors"] += 1
                    logger.error(f"Error processing article with PMID {pmid}: {e}")
                    continue
            
            session.commit()
            logger.info(f"Successfully saved articles: {stats}")
            
        except Exception as e:
            session.rollback()
            logger.error(f"Failed to save articles: {e}")
            raise
        finally:
            session.close()
        
        return stats
    
    def update_screening_results(self, screening_results: List[Dict[str, Any]], disease_name: str) -> Dict[str, int]:
        """
        Update articles with screening results from disease epidemiology screening.
        
        Args:
            screening_results: List of dictionaries with keys: 'id' (pmid), 'is_relevant'
            disease_name: Disease name to filter by
        
        Returns:
            Dictionary with statistics: {"updated": count, "not_found": count, "errors": count}
        """
        session = self.db_connection.get_session()
        stats = {"updated": 0, "not_found": 0, "errors": 0}
        
        try:
            for result in screening_results:
                try:
                    pmid = str(result['id'])
                    is_relevant = result['is_relevant']
                    
                    # Find the article and update screening result
                    # Note: Since pmcid is now part of primary key, we need to find by pmid and disease first
                    # This assumes screening results contain only pmid and we'll update all matching records
                    articles = session.query(DimArticles).filter(
                        DimArticles.pmid == pmid,
                        DimArticles.disease_name == disease_name
                    ).all()
                    
                    if articles:
                        for article in articles:
                            article.is_relevant = is_relevant
                        stats["updated"] += len(articles)
                        logger.debug(f"Updated screening result for {len(articles)} articles with PMID: {pmid}")
                    else:
                        stats["not_found"] += 1
                        logger.warning(f"No articles found with PMID {pmid} for disease {disease_name}")
                    # Continue to next result
                        
                except Exception as e:
                    stats["errors"] += 1
                    logger.error(f"Error updating screening result for {result.get('id', 'unknown')}: {e}")
                    continue
            
            session.commit()
            logger.info(f"Successfully updated screening results: {stats}")
            
        except Exception as e:
            session.rollback()
            logger.error(f"Failed to update screening results: {e}")
            raise
        finally:
            session.close()
        
        return stats
    
    def update_fulltext_status(self, fulltext_results: List[Dict[str, Any]], disease_name: str) -> Dict[str, int]:
        """
        Update articles with full-text download status and file paths.
        
        Args:
            fulltext_results: List of dictionaries with keys: 'pmcid', 'status_code', 'fulltext_path'
            disease_name: Disease name to filter by
        
        Returns:
            Dictionary with statistics: {"updated": count, "not_found": count, "errors": count}
        """
        session = self.db_connection.get_session()
        stats = {"updated": 0, "not_found": 0, "errors": 0}
        
        try:
            for result in fulltext_results:
                try:
                    pmcid = result['pmcid']
                    status_code = result['status_code']
                    fulltext_path = result.get('fulltext_path')
                    
                    # Find the article by PMCID and update fulltext status
                    article = session.query(DimArticles).filter(
                        DimArticles.pmcid == pmcid,
                        DimArticles.disease_name == disease_name
                    ).first()
                    
                    if article:
                        article.fulltext_status = status_code
                        article.fulltext_path = fulltext_path
                        stats["updated"] += 1
                        logger.debug(f"Updated fulltext status for PMCID: {pmcid}")
                    else:
                        stats["not_found"] += 1
                        logger.warning(f"Article with PMCID {pmcid} not found for fulltext update")
                        
                except Exception as e:
                    stats["errors"] += 1
                    logger.error(f"Error updating fulltext status for {result.get('pmcid', 'unknown')}: {e}")
                    continue
            
            session.commit()
            logger.info(f"Successfully updated fulltext status: {stats}")
            
        except Exception as e:
            session.rollback()
            logger.error(f"Failed to update fulltext status: {e}")
            raise
        finally:
            session.close()
        
        return stats
    
    def get_articles_by_criteria(
        self, 
        is_relevant: Optional[bool] = None,
        fulltext_status: Optional[str] = None,
        limit: Optional[int] = None
    ) -> List[DimArticles]:
        """
        Retrieve articles based on specified criteria.
        
        Args:
            is_relevant: Filter by screening relevance (True/False/None)
            fulltext_status: Filter by fulltext status ('SUCCESS'/'FAILED'/None)
            limit: Maximum number of articles to return
        
        Returns:
            List of DimArticles objects
        """
        session = self.db_connection.get_session()
        
        try:
            query = session.query(DimArticles)
            
            if is_relevant is not None:
                query = query.filter(DimArticles.is_relevant.is_(is_relevant))
            
            if fulltext_status is not None:
                query = query.filter(DimArticles.fulltext_status == fulltext_status)
            
            if limit is not None:
                query = query.limit(limit)
            
            articles = query.all()
            logger.info(f"Retrieved {len(articles)} articles with specified criteria")
            return articles
            
        except Exception as e:
            logger.error(f"Failed to retrieve articles: {e}")
            raise
        finally:
            session.close()
    
    def get_article_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about articles in the database.
        
        Returns:
            Dictionary with various statistics
        """
        session = self.db_connection.get_session()
        
        try:
            total_articles = session.query(DimArticles).count()
            relevant_articles = session.query(DimArticles).filter(
                DimArticles.is_relevant.is_(True)
            ).count()
            successful_fulltext = session.query(DimArticles).filter(
                DimArticles.fulltext_status == 'SUCCESS'
            ).count()
            failed_fulltext = session.query(DimArticles).filter(
                DimArticles.fulltext_status == 'FAILED'
            ).count()
            
            stats = {
                "total_articles": total_articles,
                "relevant_articles": relevant_articles,
                "successful_fulltext": successful_fulltext,
                "failed_fulltext": failed_fulltext,
                "pending_screening": total_articles - relevant_articles - session.query(DimArticles).filter(
                    DimArticles.is_relevant.is_(False)
                ).count(),
                "pending_fulltext": total_articles - successful_fulltext - failed_fulltext
            }
            
            logger.info(f"Article statistics: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get article statistics: {e}")
            raise
        finally:
            session.close()
    
    def count_existing_articles_for_disease(self, disease_name: str) -> int:
        """
        Count existing articles for a specific disease in the database.
        
        Args:
            disease_name: Disease name to search for
        
        Returns:
            Number of existing articles for the disease
        """
        session = self.db_connection.get_session()
        
        try:
            count = session.query(DimArticles).filter(
                DimArticles.disease_name == disease_name
            ).count()
            
            logger.info(f"Found {count} existing articles for disease: {disease_name}")
            return count
            
        except Exception as e:
            logger.error(f"Failed to count existing articles: {e}")
            raise
        finally:
            session.close()
    
    def get_articles_needing_screening(self, disease_name: str) -> List[DimArticles]:
        """
        Get articles that need screening (is_relevant is None) for a specific disease.
        
        Args:
            disease_name: Disease name to filter by
        
        Returns:
            List of articles needing screening
        """
        session = self.db_connection.get_session()
        
        try:
            articles = session.query(DimArticles).filter(
                DimArticles.disease_name == disease_name,
                DimArticles.is_relevant.is_(None)
            ).all()
            
            logger.info(f"Found {len(articles)} articles needing screening for disease: {disease_name}")
            return articles
            
        except Exception as e:
            logger.error(f"Failed to get articles needing screening: {e}")
            raise
        finally:
            session.close()
    
    def get_articles_needing_fulltext(self, disease_name: str) -> List[DimArticles]:
        """
        Get articles that need fulltext download (fulltext_status is None) for a specific disease.
        Only includes articles that are marked as relevant.
        
        Args:
            disease_name: Disease name to filter by
        
        Returns:
            List of articles needing fulltext download
        """
        session = self.db_connection.get_session()
        
        try:
            articles = session.query(DimArticles).filter(
                DimArticles.disease_name == disease_name,
                DimArticles.is_relevant.is_(True),
                DimArticles.fulltext_status.is_(None)
            ).all()
            
            logger.info(f"Found {len(articles)} articles needing fulltext download for disease: {disease_name}")
            return articles
            
        except Exception as e:
            logger.error(f"Failed to get articles needing fulltext: {e}")
            raise
        finally:
            session.close()
    
    def get_articles_needing_extraction(self, disease_name: str) -> List[DimArticles]:
        """
        Get articles that need data extraction based on sophisticated criteria:
        1. Articles must be relevant (is_relevant=True) 
        2. Articles must have successful fulltext (fulltext_status='SUCCESS')
        3. Articles must be missing successful extraction for at least one of the 4 extraction types
        
        Args:
            disease_name: Disease name to filter by
        
        Returns:
            List of articles that need data extraction
        """
        session = self.db_connection.get_session()
        
        try:
            # Define the 4 extraction task types
            extraction_task_types = [
                'point_prevalence_extraction',
                'period_prevalence_extraction', 
                'incidence_rate_extraction',
                'cumulative_incidence_extraction'
            ]
            
            # Get all relevant articles with successful fulltext
            base_articles = session.query(DimArticles).filter(
                DimArticles.disease_name == disease_name,
                DimArticles.is_relevant.is_(True),
                DimArticles.fulltext_status == 'SUCCESS'
            ).all()
            
            articles_needing_extraction = []
            
            for article in base_articles:
                # Check if this article has successful extraction for all 4 task types
                successful_extractions = session.query(DimExtraction).filter(
                    DimExtraction.pmid == article.pmid,
                    DimExtraction.pmcid == article.pmcid,
                    DimExtraction.extraction_status == 'success',
                    DimExtraction.task_type.in_(extraction_task_types)
                ).count()
                
                # If not all 4 extraction types are successful, this article needs extraction
                if successful_extractions < len(extraction_task_types):
                    articles_needing_extraction.append(article)
            
            logger.info(f"Found {len(articles_needing_extraction)} articles needing data extraction for disease: {disease_name}")
            logger.debug(f"Total relevant articles with fulltext: {len(base_articles)}")
            logger.debug(f"Articles with incomplete extraction: {len(articles_needing_extraction)}")
            
            return articles_needing_extraction
            
        except Exception as e:
            logger.error(f"Failed to get articles needing extraction: {e}")
            raise
        finally:
            session.close()
