"""
Database module for epidemiology research system.
Provides database connection management, article operations, and epidemiological data extraction.
"""

from .connection import DatabaseConnection
from .models import (
    DimArticles, 
    FactPointPrevalence, 
    FactPeriodPrevalence, 
    FactIncidenceRate, 
    FactCumulativeIncidence
)
from .article_manager import ArticleManager
from .extraction_manager import ExtractionManager

__all__ = [
    "DatabaseConnection", 
    "DimArticles", 
    "FactPointPrevalence", 
    "FactPeriodPrevalence", 
    "FactIncidenceRate", 
    "FactCumulativeIncidence",
    "ArticleManager", 
    "ExtractionManager"
]
