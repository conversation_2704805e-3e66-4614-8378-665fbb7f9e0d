-- Epidemiology Research System Database Complete Reset Script
-- This script will drop and recreate the entire database with fresh structure
-- Use with EXTREME caution - this will permanently delete ALL data and structure

-- Display current database information before purge
SELECT 'BEFORE PURGE - Database Information' AS info;
SELECT 
    SCHEMA_NAME as database_name,
    DEFAULT_CHARACTER_SET_NAME as charset,
    DEFAULT_COLLATION_NAME as collation
FROM information_schema.SCHEMATA 
WHERE SCHEMA_NAME = 'epidemiology_research';

-- Show current tables
SELECT 'Current Tables:' AS info;
SELECT 
    TABLE_NAME as table_name,
    TABLE_ROWS as estimated_rows,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) as size_mb
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'epidemiology_research'
ORDER BY TABLE_NAME;

-- Show article statistics if dim_articles exists
SELECT 'Current Article Statistics:' AS info;
SELECT 
    COUNT(*) as total_articles,
    COUNT(DISTINCT disease_name) as unique_diseases,
    SUM(CASE WHEN is_relevant = 1 THEN 1 ELSE 0 END) as relevant_articles,
    SUM(CASE WHEN is_relevant = 0 THEN 1 ELSE 0 END) as irrelevant_articles,
    SUM(CASE WHEN is_relevant IS NULL THEN 1 ELSE 0 END) as unscreened_articles,
    SUM(CASE WHEN fulltext_status = 'SUCCESS' THEN 1 ELSE 0 END) as successful_fulltext,
    SUM(CASE WHEN fulltext_status = 'FAILED' THEN 1 ELSE 0 END) as failed_fulltext,
    SUM(CASE WHEN fulltext_status IS NULL THEN 1 ELSE 0 END) as pending_fulltext
FROM epidemiology_research.dim_articles;

-- Show extraction statistics if dim_extraction exists
SELECT 'Current Extraction Statistics:' AS info;
SELECT 
    COUNT(*) as total_extraction_records,
    COUNT(DISTINCT pmid) as unique_articles_tracked,
    COUNT(DISTINCT task_type) as unique_task_types,
    SUM(CASE WHEN extraction_status = 'pending' THEN 1 ELSE 0 END) as pending_extractions,
    SUM(CASE WHEN extraction_status = 'running' THEN 1 ELSE 0 END) as running_extractions,
    SUM(CASE WHEN extraction_status = 'success' THEN 1 ELSE 0 END) as successful_extractions,
    SUM(CASE WHEN extraction_status = 'failed' THEN 1 ELSE 0 END) as failed_extractions,
    SUM(COALESCE(extracted_datapoints, 0)) as total_datapoints_extracted
FROM epidemiology_research.dim_extraction;

-- Show fact table statistics if they exist (conditionally executed)
SELECT 'Current Epidemiological Data Statistics:' AS info;

-- We'll use a procedure-like approach with conditional execution
-- First check if any fact tables exist
SET @fact_tables_exist = (
    SELECT COUNT(*) FROM information_schema.tables 
    WHERE table_schema = 'epidemiology_research' 
    AND table_name IN ('fact_point_prevalence', 'fact_period_prevalence', 'fact_incidence_rate', 'fact_cumulative_incidence')
);

-- Only show counts if database has fact tables, otherwise show "No fact tables found"
SELECT 
    CASE 
        WHEN @fact_tables_exist > 0 THEN 'Existing fact tables will be destroyed'
        ELSE 'No existing fact tables found - this appears to be a legacy database'
    END as fact_table_status;

-- Show articles by disease if data exists
SELECT 'Articles by Disease:' AS info;
SELECT 
    disease_name,
    COUNT(*) as article_count,
    SUM(CASE WHEN is_relevant = 1 THEN 1 ELSE 0 END) as relevant_count,
    SUM(CASE WHEN fulltext_status = 'SUCCESS' THEN 1 ELSE 0 END) as fulltext_count
FROM epidemiology_research.dim_articles 
GROUP BY disease_name 
ORDER BY article_count DESC;

-- Begin transaction for safety
START TRANSACTION;

-- Drop the entire database
DROP DATABASE IF EXISTS epidemiology_research;

-- Recreate the database with proper charset and collation
CREATE DATABASE epidemiology_research 
    CHARACTER SET utf8mb4 
    COLLATE utf8mb4_unicode_ci;

-- Use the newly created database
USE epidemiology_research;

-- Create the dim_articles table with the complete structure
CREATE TABLE dim_articles (
    -- Primary identifiers (composite primary key)
    pmid VARCHAR(100) NOT NULL COMMENT 'PubMed ID - primary identifier',
    disease_name VARCHAR(100) NOT NULL COMMENT 'Disease name - primary identifier for tracking studies by disease',
    pmcid VARCHAR(100) NOT NULL COMMENT 'PubMed Central ID - primary identifier',
    
    -- Article metadata from OpenAlex
    title TEXT NOT NULL COMMENT 'Article title',
    abstract TEXT NULL COMMENT 'Article abstract',
    publication_type VARCHAR(50) NULL COMMENT 'Type of publication (e.g., journal-article)',
    publication_year INT NULL COMMENT 'Year of publication',
    pdf_url TEXT NULL COMMENT 'URL to PDF version of the article',
    
    -- Screening results - added by disease epidemiology screening agent
    is_relevant BOOLEAN NULL DEFAULT NULL COMMENT 'Whether article is relevant for disease epidemiology',
    
    -- Full-text processing status - added by PubMed handler
    fulltext_status VARCHAR(20) NULL DEFAULT NULL COMMENT 'Status of full-text download (SUCCESS/FAILED)',
    fulltext_path TEXT NULL DEFAULT NULL COMMENT 'Local file path to downloaded full-text',
    
    -- Metadata fields
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp when record was created',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Timestamp when record was last updated',
    
    -- Primary key constraint
    PRIMARY KEY (pmid, disease_name, pmcid),
    
    -- Indexes for better query performance
    INDEX idx_disease_name (disease_name),
    INDEX idx_is_relevant (is_relevant),
    INDEX idx_fulltext_status (fulltext_status),
    INDEX idx_publication_year (publication_year),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB 
  CHARACTER SET utf8mb4 
  COLLATE utf8mb4_unicode_ci 
  COMMENT='Articles table for epidemiology research with disease-specific tracking';

-- Create fact_point_prevalence table
CREATE TABLE fact_point_prevalence (
    -- Primary key - auto-generated
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'Auto-generated primary key',
    
    -- Link to source article
    pmid VARCHAR(100) NOT NULL COMMENT 'Source article PMID',
    disease_name VARCHAR(100) NOT NULL COMMENT 'Disease name from source article',
    pmcid VARCHAR(100) NOT NULL COMMENT 'Source article PMCID',
    
    -- Prevalence data
    `condition` VARCHAR(200) NOT NULL COMMENT 'Disease/condition name',
    point_prevalence_percent FLOAT NOT NULL COMMENT 'Point prevalence as percentage',
    ci_lower_percent FLOAT NULL COMMENT 'Lower 95% CI for prevalence',
    ci_upper_percent FLOAT NULL COMMENT 'Upper 95% CI for prevalence',
    n_cases INT NULL COMMENT 'Number of cases',
    n_population INT NULL COMMENT 'Population size',
    year INT NOT NULL COMMENT 'Year of measurement',
    
    -- Demographics
    country VARCHAR(100) NULL COMMENT 'Country code or name',
    age_group VARCHAR(50) NULL COMMENT 'Age group',
    gender VARCHAR(20) NULL COMMENT 'Gender category',
    ethnicity VARCHAR(100) NULL COMMENT 'Ethnicity category',
    
    -- Metadata
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp when record was created',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Timestamp when record was last updated',
    
    -- Indexes for better query performance
    INDEX idx_pmid (pmid),
    INDEX idx_disease_name (disease_name),
    INDEX idx_condition (`condition`),
    INDEX idx_year (year),
    INDEX idx_country (country)
) ENGINE=InnoDB 
  CHARACTER SET utf8mb4 
  COLLATE utf8mb4_unicode_ci 
  COMMENT='Fact table for point prevalence data extracted from research articles';

-- Create fact_period_prevalence table
CREATE TABLE fact_period_prevalence (
    -- Primary key - auto-generated
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'Auto-generated primary key',
    
    -- Link to source article
    pmid VARCHAR(100) NOT NULL COMMENT 'Source article PMID',
    disease_name VARCHAR(100) NOT NULL COMMENT 'Disease name from source article',
    pmcid VARCHAR(100) NOT NULL COMMENT 'Source article PMCID',
    
    -- Prevalence data
    `condition` VARCHAR(200) NOT NULL COMMENT 'Disease/condition name',
    period_prevalence_percent FLOAT NOT NULL COMMENT 'Period prevalence as percentage',
    ci_lower_percent FLOAT NULL COMMENT 'Lower 95% CI for prevalence',
    ci_upper_percent FLOAT NULL COMMENT 'Upper 95% CI for prevalence',
    n_cases INT NULL COMMENT 'Number of cases',
    n_population INT NULL COMMENT 'Population size',
    start_year INT NOT NULL COMMENT 'Start year of measurement',
    end_year INT NOT NULL COMMENT 'End year of measurement',
    
    -- Demographics
    country VARCHAR(100) NULL COMMENT 'Country code or name',
    age_group VARCHAR(50) NULL COMMENT 'Age group',
    gender VARCHAR(20) NULL COMMENT 'Gender category',
    ethnicity VARCHAR(100) NULL COMMENT 'Ethnicity category',
    
    -- Metadata
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp when record was created',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Timestamp when record was last updated',
    
    -- Indexes for better query performance
    INDEX idx_pmid (pmid),
    INDEX idx_disease_name (disease_name),
    INDEX idx_condition (`condition`),
    INDEX idx_start_year (start_year),
    INDEX idx_end_year (end_year),
    INDEX idx_country (country)
) ENGINE=InnoDB 
  CHARACTER SET utf8mb4 
  COLLATE utf8mb4_unicode_ci 
  COMMENT='Fact table for period prevalence data extracted from research articles';

-- Create fact_incidence_rate table
CREATE TABLE fact_incidence_rate (
    -- Primary key - auto-generated
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'Auto-generated primary key',
    
    -- Link to source article
    pmid VARCHAR(100) NOT NULL COMMENT 'Source article PMID',
    disease_name VARCHAR(100) NOT NULL COMMENT 'Disease name from source article',
    pmcid VARCHAR(100) NOT NULL COMMENT 'Source article PMCID',
    
    -- Incidence data
    `condition` VARCHAR(200) NOT NULL COMMENT 'Disease/condition name',
    incidence_rate FLOAT NOT NULL COMMENT 'Incidence rate per 100,000 person-years',
    ci_lower FLOAT NULL COMMENT 'Lower 95% CI for incidence rate',
    ci_upper FLOAT NULL COMMENT 'Upper 95% CI for incidence rate',
    n_cases INT NULL COMMENT 'Number of incident cases',
    person_years FLOAT NULL COMMENT 'Total person-years at risk',
    start_year INT NOT NULL COMMENT 'Start year of measurement',
    end_year INT NOT NULL COMMENT 'End year of measurement',
    
    -- Demographics
    country VARCHAR(100) NULL COMMENT 'Country code or name',
    age_group VARCHAR(50) NULL COMMENT 'Age group',
    gender VARCHAR(20) NULL COMMENT 'Gender category',
    ethnicity VARCHAR(100) NULL COMMENT 'Ethnicity category',
    
    -- Metadata
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp when record was created',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Timestamp when record was last updated',
    
    -- Indexes for better query performance
    INDEX idx_pmid (pmid),
    INDEX idx_disease_name (disease_name),
    INDEX idx_condition (`condition`),
    INDEX idx_start_year (start_year),
    INDEX idx_end_year (end_year),
    INDEX idx_country (country)
) ENGINE=InnoDB 
  CHARACTER SET utf8mb4 
  COLLATE utf8mb4_unicode_ci 
  COMMENT='Fact table for incidence rate data extracted from research articles';

-- Create fact_cumulative_incidence table
CREATE TABLE fact_cumulative_incidence (
    -- Primary key - auto-generated
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'Auto-generated primary key',
    
    -- Link to source article
    pmid VARCHAR(100) NOT NULL COMMENT 'Source article PMID',
    disease_name VARCHAR(100) NOT NULL COMMENT 'Disease name from source article',
    pmcid VARCHAR(100) NOT NULL COMMENT 'Source article PMCID',
    
    -- Incidence data
    `condition` VARCHAR(200) NOT NULL COMMENT 'Disease/condition name',
    cumulative_incidence_percent FLOAT NOT NULL COMMENT 'Cumulative incidence as percentage',
    ci_lower_percent FLOAT NULL COMMENT 'Lower 95% CI for cumulative incidence',
    ci_upper_percent FLOAT NULL COMMENT 'Upper 95% CI for cumulative incidence',
    n_new_cases INT NULL COMMENT 'Number of new cases',
    n_at_risk_start INT NULL COMMENT 'Number at risk at start',
    start_year INT NOT NULL COMMENT 'Start year of measurement',
    end_year INT NOT NULL COMMENT 'End year of measurement',
    
    -- Demographics
    country VARCHAR(100) NULL COMMENT 'Country code or name',
    age_group VARCHAR(50) NULL COMMENT 'Age group',
    gender VARCHAR(20) NULL COMMENT 'Gender category',
    ethnicity VARCHAR(100) NULL COMMENT 'Ethnicity category',
    
    -- Metadata
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp when record was created',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Timestamp when record was last updated',
    
    -- Indexes for better query performance
    INDEX idx_pmid (pmid),
    INDEX idx_disease_name (disease_name),
    INDEX idx_condition (`condition`),
    INDEX idx_start_year (start_year),
    INDEX idx_end_year (end_year),
    INDEX idx_country (country)
) ENGINE=InnoDB 
  CHARACTER SET utf8mb4 
  COLLATE utf8mb4_unicode_ci 
  COMMENT='Fact table for cumulative incidence data extracted from research articles';

-- Create dim_extraction table for tracking extraction status
CREATE TABLE dim_extraction (
    -- Primary key - auto-generated
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'Auto-generated primary key',
    
    -- Link to source article (without disease_name as requested)
    pmid VARCHAR(100) NOT NULL COMMENT 'Source article PMID',
    pmcid VARCHAR(100) NOT NULL COMMENT 'Source article PMCID',
    
    -- Extraction task information
    task_type VARCHAR(50) NOT NULL COMMENT 'Type of extraction task (point_prevalence_extraction, period_prevalence_extraction, incidence_rate_extraction, cumulative_incidence_extraction)',
    
    -- Extraction status and results
    extraction_status VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT 'Status of extraction (pending, running, success, failed)',
    extracted_datapoints INT DEFAULT 0 COMMENT 'Number of datapoints successfully extracted',
    extraction_summary TEXT COMMENT 'Summary or error message from extraction process',
    
    -- Full extraction response (for debugging and analysis)
    extraction_response JSON COMMENT 'Complete response object from extraction agent',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp when extraction record was created',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Timestamp when extraction record was last updated',
    started_at TIMESTAMP NULL COMMENT 'Timestamp when extraction process started',
    completed_at TIMESTAMP NULL COMMENT 'Timestamp when extraction process completed',
    
    -- Indexes for efficient querying
    INDEX idx_pmid_pmcid (pmid, pmcid),
    INDEX idx_task_type (task_type),
    INDEX idx_extraction_status (extraction_status),
    INDEX idx_pmid_task (pmid, task_type),
    INDEX idx_status_task (extraction_status, task_type),
    INDEX idx_created_at (created_at),
    INDEX idx_completed_at (completed_at)
) ENGINE=InnoDB 
  CHARACTER SET utf8mb4 
  COLLATE utf8mb4_unicode_ci 
  COMMENT='Dimension table for tracking epidemiological data extraction status and results';

-- Grant permissions to the application user
GRANT ALL PRIVILEGES ON epidemiology_research.* TO 'epidemiology_user'@'localhost';
FLUSH PRIVILEGES;

-- Verify the new database structure
SELECT 'AFTER RECREATION - Database Information' AS info;
SELECT 
    SCHEMA_NAME as database_name,
    DEFAULT_CHARACTER_SET_NAME as charset,
    DEFAULT_COLLATION_NAME as collation
FROM information_schema.SCHEMATA 
WHERE SCHEMA_NAME = 'epidemiology_research';

-- Show recreated tables
SELECT 'Recreated Tables:' AS info;
SELECT 
    TABLE_NAME as table_name,
    TABLE_ROWS as estimated_rows,
    ENGINE as storage_engine,
    TABLE_COLLATION as collation
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'epidemiology_research'
ORDER BY TABLE_NAME;

-- Show table structure
SELECT 'Table Structure - dim_articles:' AS info;
DESCRIBE epidemiology_research.dim_articles;

-- Show indexes for all tables
SELECT 'Table Indexes:' AS info;
SELECT 
    TABLE_NAME as table_name,
    INDEX_NAME as index_name,
    COLUMN_NAME as column_name,
    NON_UNIQUE as non_unique,
    INDEX_TYPE as index_type
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'epidemiology_research'
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- Verify empty tables
SELECT 'Verification - Record Counts:' AS info;
SELECT 
    (SELECT COUNT(*) FROM epidemiology_research.dim_articles) as dim_articles,
    (SELECT COUNT(*) FROM epidemiology_research.dim_extraction) as dim_extraction,
    (SELECT COUNT(*) FROM epidemiology_research.fact_point_prevalence) as fact_point_prevalence,
    (SELECT COUNT(*) FROM epidemiology_research.fact_period_prevalence) as fact_period_prevalence,
    (SELECT COUNT(*) FROM epidemiology_research.fact_incidence_rate) as fact_incidence_rate,
    (SELECT COUNT(*) FROM epidemiology_research.fact_cumulative_incidence) as fact_cumulative_incidence;

-- Display success message
SELECT 'Database recreation completed successfully!' AS result;
SELECT 'The epidemiology_research database has been completely reset with fresh structure' AS result;

-- Commit the transaction
COMMIT;

-- Final status
SELECT 'Ready for new data!' AS status;
