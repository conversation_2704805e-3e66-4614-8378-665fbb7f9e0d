# Database Management Scripts

This directory contains scripts for managing the Epidemiology Research System database.

## ⚠️ CRITICAL WARNING

**These scripts perform COMPLETE DATABASE DESTRUCTION and recreation!**

Unlike simple data purges, these scripts will:
- 🗑️ **DROP the entire database** (structure + data)
- 💥 **DESTROY all tables, indexes, constraints**
- 🔥 **DELETE all data permanently and irreversibly**
- 🏗️ **RECREATE the database with fresh structure**

**This is EXTREMELY DESTRUCTIVE - use with maximum caution!**

## Files Overview

### 1. `purge_database.sql`
Complete SQL script for database destruction and recreation:
- Shows comprehensive pre-destruction statistics
- Drops entire `epidemiology_research` database
- Recreates database with proper charset/collation
- Creates fresh `dim_articles` table with all indexes
- Grants proper permissions to application user
- Verifies successful recreation

### 2. `purge_database.py`
Python wrapper with enhanced safety features:
- **Double confirmation** requiring specific text input
- Root MySQL connection for database operations
- Comprehensive error handling and verification
- Post-recreation validation
- Interactive password prompts

### 3. `purge_db.sh`
Shell script for convenient execution:
- Project root detection and navigation
- Virtual environment auto-activation
- Color-coded warnings and output
- Comprehensive help documentation
- Exit status handling

### 4. `README.md`
This comprehensive documentation file.

## Usage Methods

### Method 1: Shell Script (Recommended)

**From project root:**
```bash
# Interactive mode with double confirmation
src/db/scripts/purge_db.sh

# Force mode (EXTREMELY DANGEROUS!)
src/db/scripts/purge_db.sh --force

# Show help and warnings
src/db/scripts/purge_db.sh --help
```

### Method 2: Python Script

**From project root:**
```bash
# Interactive mode with confirmations
python src/db/scripts/purge_database.py

# Force mode (skip confirmations)
python src/db/scripts/purge_database.py --force
```

### Method 3: Direct SQL Execution

**Using MySQL root user:**
```bash
# From project root
mysql -u root -p < src/db/scripts/purge_database.sql
```

## Safety Features

### 🔒 Multi-Level Confirmations

**Level 1: Acknowledgment**
- User must type `DESTROY` to acknowledge risks

**Level 2: Final Confirmation**  
- User must type `YES I UNDERSTAND` to proceed

**Force Mode Override**
- `--force` flag bypasses all confirmations
- Should ONLY be used in automated testing environments

### 🛡️ Technical Safeguards

- **Root connection validation** before proceeding
- **Script existence verification** before execution
- **Transaction-based operations** where applicable
- **Post-recreation verification** to ensure success
- **Detailed error reporting** at each step

### 📊 Comprehensive Reporting

**Pre-Destruction Statistics:**
- Database information (charset, collation)
- Table listing with sizes
- Article counts by disease
- Screening and fulltext statistics

**Post-Recreation Verification:**
- Database recreation confirmation
- Table structure validation
- Index verification
- Permission grants confirmation

## Database Structure Created

The script recreates all tables in the epidemiology research database:

### `dim_articles` Table (Dimension - Articles)
**Columns:**
- `pmid` + `disease_name` + `pmcid` (composite primary key)
- `title`, `abstract` (article metadata)
- `publication_type`, `publication_year`, `pdf_url`
- `is_relevant` (screening results)
- `fulltext_status`, `fulltext_path` (download status)
- `created_at`, `updated_at` (timestamps)

### `fact_point_prevalence` Table (Fact - Point Prevalence Data)
**Columns:**
- `id` (auto-increment primary key)
- `pmid`, `disease_name`, `pmcid` (article reference)
- `condition`, `point_prevalence_percent` (prevalence data)
- `ci_lower_percent`, `ci_upper_percent` (confidence intervals)
- `n_cases`, `n_population`, `year` (study metrics)
- `country`, `age_group`, `gender`, `ethnicity` (demographics)
- `created_at`, `updated_at` (timestamps)

### `fact_period_prevalence` Table (Fact - Period Prevalence Data)
**Columns:**
- `id` (auto-increment primary key)
- `pmid`, `disease_name`, `pmcid` (article reference)
- `condition`, `period_prevalence_percent` (prevalence data)
- `ci_lower_percent`, `ci_upper_percent` (confidence intervals)
- `n_cases`, `n_population`, `start_year`, `end_year` (study metrics)
- `country`, `age_group`, `gender`, `ethnicity` (demographics)
- `created_at`, `updated_at` (timestamps)

### `fact_incidence_rate` Table (Fact - Incidence Rate Data)
**Columns:**
- `id` (auto-increment primary key)
- `pmid`, `disease_name`, `pmcid` (article reference)
- `condition`, `incidence_rate` (rate per 100,000 person-years)
- `ci_lower`, `ci_upper` (confidence intervals)
- `n_cases`, `person_years`, `start_year`, `end_year` (study metrics)
- `country`, `age_group`, `gender`, `ethnicity` (demographics)
- `created_at`, `updated_at` (timestamps)

### `fact_cumulative_incidence` Table (Fact - Cumulative Incidence Data)
**Columns:**
- `id` (auto-increment primary key)
- `pmid`, `disease_name`, `pmcid` (article reference)
- `condition`, `cumulative_incidence_percent` (cumulative incidence data)
- `ci_lower_percent`, `ci_upper_percent` (confidence intervals)
- `n_new_cases`, `n_at_risk_start`, `start_year`, `end_year` (study metrics)
- `country`, `age_group`, `gender`, `ethnicity` (demographics)
- `created_at`, `updated_at` (timestamps)

**Common Features Across All Tables:**
- UTF8MB4 character set for full Unicode support
- Case-insensitive collation for text searches
- InnoDB storage engine for ACID compliance
- Performance indexes on commonly queried fields
- Proper foreign key relationships to source articles

## Example Output

```
🗄️  Epidemiology Research Database Complete Reset Tool
==============================================================

🚨 EXTREME WARNING: COMPLETE DATABASE DESTRUCTION
=====================================================
This operation will:
  🗑️  DROP the ENTIRE 'epidemiology_research' database
  💥 DESTROY ALL tables, indexes, and constraints
  🔥 DELETE ALL data permanently and irreversibly
  🏗️  RECREATE the database with fresh structure

Type 'DESTROY' to confirm you understand the risks: DESTROY

🔴 FINAL CONFIRMATION REQUIRED
Type 'YES I UNDERSTAND' to proceed: YES I UNDERSTAND

🔑 Setting up root database connection...
✅ Root connection string configured

📜 Loading SQL script: src/db/scripts/purge_database.sql
✅ SQL script loaded successfully

🔥 Executing complete database reset...
==============================================================
BEFORE PURGE - Database Information
database_name | charset | collation
epidemiology_research | utf8mb4 | utf8mb4_unicode_ci

Current Article Statistics:
total_articles: 150 | unique_diseases: 3 | relevant_articles: 45

Current Epidemiological Data Statistics:
point_prevalence_records: 245 | period_prevalence_records: 89 | incidence_rate_records: 156 | cumulative_incidence_records: 67

AFTER RECREATION - Database Information  
database_name | charset | collation
epidemiology_research | utf8mb4 | utf8mb4_unicode_ci

Verification - Record Counts:
dim_articles: 0 | fact_point_prevalence: 0 | fact_period_prevalence: 0 | fact_incidence_rate: 0 | fact_cumulative_incidence: 0
==============================================================
✅ Database reset completed successfully!

🔍 Verifying database recreation...
✅ Database verification successful
   - Database connection: OK
   - All table structures: OK  
   - All record counts: 0 (expected for fresh database)

🎉 Complete database reset finished!
The database has been completely recreated with fresh structure.
Ready for new data collection and processing.
```

## When to Use This Tool

### ✅ Appropriate Use Cases

1. **Development Testing**
   - Fresh environment for testing new schema changes
   - Baseline testing with clean database structure

2. **Schema Updates**
   - Major structural changes requiring table recreation
   - Index modifications or constraint updates

3. **Environment Reset**
   - Setting up clean demo environments
   - Preparing for fresh data collection projects

4. **Corruption Recovery**
   - Database corruption requiring complete rebuild
   - Fixing structural integrity issues

### ❌ Inappropriate Use Cases

1. **Production Environments**
   - NEVER run on production databases
   - Always use proper backup/restore procedures

2. **Simple Data Cleanup**
   - Use regular purge tools for data-only cleanup
   - This tool is overkill for removing records

3. **Partial Updates**
   - Use migration scripts for incremental changes
   - This tool destroys ALL structure

## Prerequisites

### Required Permissions
- MySQL **root access** for database operations
- **File system write** permissions in project directory
- **Virtual environment** with dependencies installed

### Environment Variables
```bash
# Required in .env or environment
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_ROOT_PASSWORD=your_root_password

# Application user will be recreated by the script
MYSQL_USERNAME=epidemiology_user
MYSQL_PASSWORD=EpiResearch2024!
```

### Dependencies
- Python 3.11+
- SQLAlchemy and PyMySQL
- MySQL 8.0+ server running
- Virtual environment activated

## Troubleshooting

### Common Issues

**1. Permission Denied:**
```bash
# Make script executable
chmod +x src/db/scripts/purge_db.sh
```

**2. Root Password Issues:**
```bash
# Set root password environment variable
export MYSQL_ROOT_PASSWORD="your_root_password"

# Or update .env file
echo "MYSQL_ROOT_PASSWORD=your_root_password" >> .env
```

**3. Virtual Environment Not Found:**
```bash
# From project root
uv sync
source .venv/bin/activate
```

**4. MySQL Service Not Running:**
```bash
sudo systemctl start mysql
sudo systemctl status mysql
```

**5. Database User Issues:**
```bash
# Script recreates the user, but if there are issues:
sudo mysql -e "DROP USER IF EXISTS 'epidemiology_user'@'localhost';"
sudo mysql -e "CREATE USER 'epidemiology_user'@'localhost' IDENTIFIED BY 'EpiResearch2024!';"
```

### Recovery Procedures

**If script fails midway:**
1. Check MySQL error logs: `sudo tail -f /var/log/mysql/error.log`
2. Manually verify database state: `mysql -u root -p -e "SHOW DATABASES;"`
3. Re-run the script after fixing issues
4. Use force mode only if absolutely necessary

**If verification fails:**
1. Check application user permissions
2. Verify table structure manually
3. Test connection with main application
4. Review MySQL grants: `SHOW GRANTS FOR 'epidemiology_user'@'localhost';`

## Security Considerations

🔐 **Critical Security Notes:**

1. **Root Password Protection**
   - Never store root passwords in version control
   - Use environment variables or secure prompts
   - Consider using MySQL configuration files

2. **Access Control**
   - Limit script access to authorized administrators
   - Use proper file permissions (600) for sensitive files
   - Audit script execution in production-like environments

3. **Backup Strategy**
   - Always backup before running destructive operations
   - Test restore procedures regularly
   - Document backup/restore processes

4. **Environment Isolation**
   - NEVER run on production databases
   - Use separate credentials for different environments
   - Implement proper environment variable management

## Best Practices

1. **Always backup** critical data before running
2. **Test in development** before any production-like use
3. **Document** when and why complete resets were performed
4. **Verify** database functionality after recreation
5. **Monitor** application behavior with fresh database
6. **Review** any schema changes before applying

## Support and Maintenance

For issues with database reset functionality:
1. Check MySQL server status and connectivity
2. Verify root user permissions and password
3. Review error logs for specific MySQL errors  
4. Ensure virtual environment dependencies are current
5. Test individual SQL statements if needed

**Emergency Recovery:**
If the database is left in an inconsistent state, manually recreate using the SQL script sections or restore from backup.
