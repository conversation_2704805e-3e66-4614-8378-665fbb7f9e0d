#!/usr/bin/env python3
"""
Complete Database Reset Script for Epidemiology Research System

This script completely drops and recreates the database with fresh structure.
Use with EXTREME caution - this will permanently delete ALL data and structure.

Usage:
    python src/db/scripts/purge_database.py
    python src/db/scripts/purge_database.py --force  # Skip confirmation prompt
"""

import sys
import os
import argparse
from pathlib import Path
from typing import Optional

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.db import DatabaseConnection
    from sqlalchemy import text, create_engine
    import pymysql
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the project root with dependencies installed.")
    sys.exit(1)


def confirm_complete_reset() -> bool:
    """
    Ask user for confirmation before completely resetting the database.
    
    Returns:
        True if user confirms, False otherwise
    """
    print("🚨 EXTREME WARNING: COMPLETE DATABASE RESET")
    print("=" * 60)
    print("This operation will:")
    print("• 🗑️  DROP the ENTIRE 'epidemiology_research' database")
    print("• 💥 DESTROY ALL tables, indexes, and constraints")
    print("• 🔥 DELETE ALL data permanently and irreversibly")
    print("• 🏗️  RECREATE the database with fresh structure")
    print("• ⚡ RESET all table schemas to current version")
    print("• 📊 RECREATE all fact tables (prevalence, incidence data)")
    print()
    print("⚠️  THIS IS MORE DESTRUCTIVE THAN A SIMPLE PURGE!")
    print("⚠️  ALL DATABASE STRUCTURE WILL BE LOST!")
    print("⚠️  THIS ACTION CANNOT BE UNDONE!")
    print()
    
    # First confirmation
    response1 = input("Type 'DESTROY' to confirm you understand the risks: ").strip()
    if response1.upper() != 'DESTROY':
        return False
    
    # Second confirmation
    print()
    print("🔴 FINAL CONFIRMATION REQUIRED")
    response2 = input("Type 'YES I UNDERSTAND' to proceed with complete database reset: ").strip()
    return response2.upper() == 'YES I UNDERSTAND'


def get_root_connection_string() -> str:
    """
    Get MySQL root connection string for database operations.
    Handles both password and socket authentication.
    
    Returns:
        MySQL connection string with root privileges
    """
    host = os.getenv("MYSQL_HOST", "localhost")
    port = os.getenv("MYSQL_PORT", "3306")
    username = "root"
    password = os.getenv("MYSQL_ROOT_PASSWORD") or os.getenv("MYSQL_PASSWORD")
    
    # For socket authentication (common on Ubuntu), we'll use a different approach
    if not password and host == "localhost":
        print("ℹ️  Root user appears to use socket authentication")
        print("   Will attempt to use socket-based connection")
        # Return connection string that will trigger socket authentication handling
        return f"mysql+pymysql://{username}:@{host}:{port}/"
    
    if not password:
        # Try to get root password interactively
        import getpass
        password = getpass.getpass("Enter MySQL root password (or press Enter for socket auth): ")
        if not password:
            return f"mysql+pymysql://{username}:@{host}:{port}/"
    
    return f"mysql+pymysql://{username}:{password}@{host}:{port}/"


def load_sql_script(script_path: Path) -> str:
    """
    Load the SQL script from file.
    
    Args:
        script_path: Path to the SQL script file
        
    Returns:
        SQL script content
        
    Raises:
        FileNotFoundError: If script file doesn't exist
    """
    if not script_path.exists():
        raise FileNotFoundError(f"SQL script not found: {script_path}")
    
    with open(script_path, 'r', encoding='utf-8') as f:
        return f.read()


def execute_sql_with_mysql_command(sql_content: str) -> bool:
    """
    Execute SQL statements using MySQL command line (for socket authentication).
    
    Args:
        sql_content: SQL script content
        
    Returns:
        True if successful, False otherwise
    """
    import subprocess
    import tempfile
    
    try:
        # Create a temporary file with the SQL content
        with tempfile.NamedTemporaryFile(mode='w', suffix='.sql', delete=False) as temp_file:
            temp_file.write(sql_content)
            temp_file_path = temp_file.name
        
        # Execute using sudo mysql
        print("🔧 Executing SQL using sudo mysql command...")
        result = subprocess.run(
            ['sudo', 'mysql', '--table', '-v'],
            input=sql_content,
            text=True,
            capture_output=True,
            timeout=300  # 5 minute timeout
        )
        
        # Display output
        if result.stdout:
            print("📄 SQL Output:")
            print(result.stdout)
        
        if result.stderr:
            print("⚠️  SQL Warnings/Errors:")
            print(result.stderr)
        
        # Clean up temp file
        os.unlink(temp_file_path)
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ SQL execution timed out after 5 minutes")
        return False
    except Exception as e:
        print(f"❌ Error executing SQL with command line: {e}")
        return False


def execute_sql_statements_with_root(connection_string: str, sql_content: str) -> None:
    """
    Execute SQL statements using root connection for database recreation.
    Tries PyMySQL first, falls back to command line for socket authentication.
    
    Args:
        connection_string: MySQL root connection string
        sql_content: SQL script content
    """
    # First try with PyMySQL/SQLAlchemy
    try:
        # Create engine with root privileges
        engine = create_engine(connection_string, echo=False)
        
        with engine.connect() as connection:
            # Test the connection
            connection.execute(text("SELECT 1"))
            print("✅ PyMySQL connection successful")
            
            # Set autocommit mode for DDL operations
            connection.execute(text("SET autocommit = 1"))
            
            # Split SQL content into individual statements
            statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            
            for statement in statements:
                # Skip comments and empty statements
                if statement.startswith('--') or not statement:
                    continue
                    
                try:
                    # Execute the statement
                    result = connection.execute(text(statement))
                    
                    # If it's a SELECT statement, fetch and display results
                    if statement.upper().strip().startswith('SELECT'):
                        rows = result.fetchall()
                        if rows:
                            # Get column names
                            columns = result.keys() if hasattr(result, 'keys') else []
                            
                            # Display results in a simple format
                            if columns and len(columns) > 0:
                                print(" | ".join(str(col) for col in columns))
                                print("-" * 60)
                            
                            for row in rows:
                                print(" | ".join(str(val) if val is not None else 'NULL' for val in row))
                            print()
                    
                except Exception as e:
                    # Some errors are expected (like trying to query non-existent tables)
                    if "doesn't exist" in str(e).lower() or "unknown database" in str(e).lower():
                        print(f"ℹ️  Expected: {str(e)}")
                    else:
                        print(f"⚠️  Error executing statement: {e}")
                        print(f"Statement: {statement[:100]}...")
                        continue
            
            engine.dispose()
            
    except Exception as e:
        print(f"ℹ️  PyMySQL connection failed: {e}")
        print("🔄 Falling back to MySQL command line for socket authentication...")
        
        # Fall back to command line execution
        if not execute_sql_with_mysql_command(sql_content):
            raise Exception("Both PyMySQL and command line execution failed")


def verify_database_recreation() -> bool:
    """
    Verify that the database was recreated successfully.
    
    Returns:
        True if verification successful, False otherwise
    """
    try:
        # Try to connect with the application user
        db_connection = DatabaseConnection()
        db_connection.connect()
        
        session = db_connection.get_session()
        try:
            # Test basic operations and verify all tables
            tables_to_check = [
                "dim_articles",
                "dim_extraction",
                "fact_point_prevalence", 
                "fact_period_prevalence",
                "fact_incidence_rate",
                "fact_cumulative_incidence"
            ]
            
            table_counts = {}
            total_records = 0
            
            for table in tables_to_check:
                result = session.execute(text(f"SELECT COUNT(*) FROM {table}"))
                count = result.scalar() or 0  # Handle potential None values
                table_counts[table] = count
                total_records += count
            
            if total_records == 0:
                print("✅ Database verification successful")
                print(f"   - Database connection: OK")
                print(f"   - All table structures: OK")
                print(f"   - All record counts: {total_records} (expected for fresh database)")
                
                # Show individual table counts for transparency
                print("   - Individual table counts:")
                for table, count in table_counts.items():
                    print(f"     • {table}: {count}")
                return True
            else:
                print(f"⚠️  Unexpected total record count: {total_records}")
                print("   Table counts:")
                for table, count in table_counts.items():
                    print(f"     • {table}: {count}")
                return False
                
        finally:
            session.close()
            db_connection.close()
            
    except Exception as e:
        print(f"❌ Database verification failed: {e}")
        return False


def main():
    """Main function to execute the complete database reset."""
    parser = argparse.ArgumentParser(
        description="Completely drop and recreate the epidemiology research database"
    )
    parser.add_argument('--force', action='store_true', help='Skip confirmation prompts')
    args = parser.parse_args()
    
    print("🗄️  Epidemiology Research Database Complete Reset Tool")
    print("=" * 60)
    
    # Check if user wants to proceed without confirmation
    if not args.force:
        if not confirm_complete_reset():
            print("❌ Operation cancelled by user.")
            return False
    else:
        print("🚀 Force mode enabled - skipping confirmation")
        print("⚠️  PROCEEDING WITH COMPLETE DATABASE DESTRUCTION!")
    
    # Get root connection string
    try:
        print("\n🔑 Setting up root database connection...")
        root_connection_string = get_root_connection_string()
        print("✅ Root connection string configured")
    except Exception as e:
        print(f"❌ Failed to configure root connection: {e}")
        return False
    
    # Load SQL script
    try:
        script_path = Path(__file__).parent / "purge_database.sql"
        print(f"\n📜 Loading SQL script: {script_path}")
        sql_content = load_sql_script(script_path)
        print("✅ SQL script loaded successfully")
    except FileNotFoundError as e:
        print(f"❌ {e}")
        return False
    except Exception as e:
        print(f"❌ Error loading SQL script: {e}")
        return False
    
    # Execute complete database reset
    try:
        print("\n🔥 Executing complete database reset...")
        print("=" * 60)
        execute_sql_statements_with_root(root_connection_string, sql_content)
        print("=" * 60)
        print("✅ Database reset completed successfully!")
        
    except Exception as e:
        print(f"❌ Database reset operation failed: {e}")
        return False
    
    # Verify the recreation
    print("\n🔍 Verifying database recreation...")
    if verify_database_recreation():
        print("✅ Verification completed successfully!")
    else:
        print("⚠️  Verification had issues - please check manually")
    
    print("\n🎉 Complete database reset finished!")
    print("The database has been completely recreated with fresh structure:")
    print("  • dim_articles (article metadata)")
    print("  • dim_extraction (extraction status tracking)")
    print("  • fact_point_prevalence (point prevalence data)")
    print("  • fact_period_prevalence (period prevalence data)")  
    print("  • fact_incidence_rate (incidence rate data)")
    print("  • fact_cumulative_incidence (cumulative incidence data)")
    print("Ready for new data collection and epidemiological data extraction.")
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
