#!/bin/bash

# Complete Database Reset Script for Epidemiology Research System
# This script completely drops and recreates the database with fresh structure

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

echo -e "${PURPLE}🗄️  Epidemiology Research Database Complete Reset Tool${NC}"
echo "=============================================================="

# Get the project root directory (3 levels up from this script)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"

# Change to project root
cd "$PROJECT_ROOT" || {
    echo -e "${RED}❌ Could not change to project root directory${NC}"
    exit 1
}

echo -e "${BLUE}📁 Project root: $PROJECT_ROOT${NC}"

# Check if virtual environment is activated
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo -e "${YELLOW}⚠️  Virtual environment not detected${NC}"
    echo "Attempting to activate .venv..."
    
    if [ -f ".venv/bin/activate" ]; then
        source .venv/bin/activate
        echo -e "${GREEN}✅ Virtual environment activated${NC}"
    else
        echo -e "${RED}❌ Virtual environment not found${NC}"
        echo "Please run: uv sync or create a virtual environment"
        exit 1
    fi
fi

# Check if required files exist
PYTHON_SCRIPT="src/db/scripts/purge_database.py"
SQL_SCRIPT="src/db/scripts/purge_database.sql"

if [ ! -f "$PYTHON_SCRIPT" ]; then
    echo -e "${RED}❌ $PYTHON_SCRIPT not found${NC}"
    exit 1
fi

if [ ! -f "$SQL_SCRIPT" ]; then
    echo -e "${RED}❌ $SQL_SCRIPT not found${NC}"
    exit 1
fi

# Parse command line arguments
FORCE_MODE=false
if [[ "$1" == "--force" || "$1" == "-f" ]]; then
    FORCE_MODE=true
fi

# Show usage if help requested
if [[ "$1" == "--help" || "$1" == "-h" ]]; then
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "⚠️  WARNING: This script completely drops and recreates the database!"
    echo ""
    echo "Options:"
    echo "  --force, -f    Skip confirmation prompts (DANGEROUS!)"
    echo "  --help, -h     Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0             # Interactive mode with double confirmation"
    echo "  $0 --force     # Force mode, skip all confirmations (USE WITH EXTREME CAUTION)"
    echo ""
    echo "🚨 EXTREME WARNING:"
    echo "   This operation will COMPLETELY DESTROY the database and recreate it!"
    echo "   This is much more destructive than a simple purge!"
    echo "   ALL database structure and data will be permanently lost!"
    exit 0
fi

# Show warning about the destructive nature
echo ""
echo -e "${RED}🚨 EXTREME WARNING: COMPLETE DATABASE DESTRUCTION${NC}"
echo -e "${RED}=====================================================${NC}"
echo -e "${YELLOW}This operation will:${NC}"
echo -e "${RED}  🗑️  DROP the ENTIRE 'epidemiology_research' database${NC}"
echo -e "${RED}  💥 DESTROY ALL tables, indexes, and constraints${NC}"
echo -e "${RED}  🔥 DELETE ALL data permanently and irreversibly${NC}"
echo -e "${RED}  🏗️  RECREATE the database with fresh structure${NC}"
echo ""
echo -e "${YELLOW}This is MORE DESTRUCTIVE than a simple data purge!${NC}"
echo ""

# Execute the Python script
echo -e "${BLUE}🚀 Starting complete database reset process...${NC}"
echo ""

if $FORCE_MODE; then
    echo -e "${RED}⚠️  FORCE MODE ENABLED - SKIPPING ALL CONFIRMATIONS!${NC}"
    python "$PYTHON_SCRIPT" --force
else
    python "$PYTHON_SCRIPT"
fi

# Check exit status
EXIT_CODE=$?
if [ $EXIT_CODE -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 Complete database reset completed successfully!${NC}"
    echo -e "${GREEN}The database has been completely recreated with fresh structure.${NC}"
    echo -e "${BLUE}Ready for new data collection and processing.${NC}"
else
    echo ""
    echo -e "${RED}❌ Database reset failed!${NC}"
    echo -e "${YELLOW}Please check the error messages above and try again.${NC}"
    exit $EXIT_CODE
fi

# Show next steps
echo ""
echo -e "${BLUE}📋 Next Steps:${NC}"
echo "  1. Verify database connection: mysql -u epidemiology_user -p"
echo "  2. Run the main CLI to start collecting data: python cli.py"
echo "  3. Check table structure: mysql -u epidemiology_user -p -e 'SHOW TABLES FROM epidemiology_research;'"
echo "  4. Check dim_extraction table: mysql -u epidemiology_user -p -e 'DESCRIBE epidemiology_research.dim_extraction;'"
