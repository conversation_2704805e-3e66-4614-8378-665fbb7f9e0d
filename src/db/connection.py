"""
Database connection management for MySQL database.
"""

import os
from typing import Optional
from sqlalchemy import create_engine, Engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.declarative import declarative_base
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

# Base class for all database models
Base = declarative_base()

logger = logging.getLogger(__name__)


class DatabaseConnection:
    """
    Manages MySQL database connections for the epidemiology research system.
    """
    
    def __init__(self, connection_string: Optional[str] = None):
        """
        Initialize database connection.
        
        Args:
            connection_string: Optional MySQL connection string. If not provided,
                             it will be constructed from environment variables.
        """
        self.engine: Optional[Engine] = None
        self.SessionLocal: Optional[sessionmaker] = None
        self._connection_string = connection_string or self._get_connection_string()
        
    def _get_connection_string(self) -> str:
        """
        Construct MySQL connection string from environment variables.
        
        Returns:
            MySQL connection string
            
        Raises:
            ValueError: If required environment variables are not set
        """
        host = os.getenv("MYSQL_HOST", "localhost")
        port = os.getenv("MYSQL_PORT", "3306")
        database = os.getenv("MYSQL_DATABASE", "epidemiology_research")
        username = os.getenv("MYSQL_USERNAME", "root")
        password = os.getenv("MYSQL_PASSWORD")
        
        if not password:
            raise ValueError(
                "MYSQL_PASSWORD environment variable is required. "
                "Please set it in your .env file or environment."
            )
        
        return f"mysql+pymysql://{username}:{password}@{host}:{port}/{database}"
    
    def connect(self) -> None:
        """
        Establish connection to the MySQL database.
        
        Raises:
            Exception: If connection fails
        """
        try:
            self.engine = create_engine(
                self._connection_string,
                echo=False,  # Set to True for SQL debugging
                pool_pre_ping=True,  # Validate connections before use
                pool_recycle=3600,   # Recycle connections every hour
            )
            
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            # Test the connection
            with self.engine.connect() as conn:
                from sqlalchemy import text
                conn.execute(text("SELECT 1"))
                
            logger.info("Successfully connected to MySQL database")
            
        except Exception as e:
            logger.error(f"Failed to connect to MySQL database: {e}")
            raise
    
    def create_tables(self) -> None:
        """
        Create all database tables if they don't exist.
        
        Raises:
            Exception: If table creation fails
        """
        if not self.engine:
            raise RuntimeError("Database connection not established. Call connect() first.")
        
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Failed to create database tables: {e}")
            raise
    
    def get_session(self) -> Session:
        """
        Get a new database session.
        
        Returns:
            SQLAlchemy session
            
        Raises:
            RuntimeError: If connection not established
        """
        if not self.SessionLocal:
            raise RuntimeError("Database connection not established. Call connect() first.")
        
        return self.SessionLocal()
    
    def close(self) -> None:
        """
        Close the database connection.
        """
        if self.engine:
            self.engine.dispose()
            logger.info("Database connection closed")
