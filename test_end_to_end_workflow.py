#!/usr/bin/env python3
"""
End-to-end test to verify the complete research workflow works correctly.
"""

import sys
import os
import threading
import time
from unittest.mock import MagicMock

# Add src directory to Python path
sys.path.append('src')

def test_end_to_end_workflow():
    """Test the complete end-to-end research workflow."""
    
    print("🧪 Testing End-to-End Research Workflow")
    print("=" * 50)
    
    # Import required modules
    from streamlit_app.utils.progress_manager import get_progress_manager, sync_progress_to_session_state
    from streamlit_app.pages.research_interface import run_research_pipeline_thread_safe
    
    # Mock session state
    class MockSessionState:
        def __init__(self):
            self.research_progress = {
                "is_running": False,
                "current_step": "Idle",
                "disease_name": None,
                "country": None,
                "total_steps": 6,
                "completed_steps": 0,
                "step_details": {},
                "results": {},
                "errors": [],
                "last_updated": None
            }
            self.operation_status = {}
        
        def get(self, key, default=None):
            return getattr(self, key, default)
        
        def update(self, data):
            for key, value in data.items():
                setattr(self, key, value)
    
    # Create mock session state
    mock_session_state = MockSessionState()
    
    # Test parameters
    disease_name = "asthma"
    country = "US"
    time_period = "2020-2025"
    num_works = 2
    
    print(f"Testing workflow with: {disease_name}, {country}, {time_period}, {num_works} works")
    
    # Step 1: Initialize progress manager
    progress_manager = get_progress_manager()
    progress_manager.reset_progress()
    progress_manager.set_running(disease_name, country, total_steps=6)
    
    print("✅ Step 1: Progress manager initialized")
    
    # Step 2: Sync initial state to session state
    sync_progress_to_session_state(mock_session_state)
    
    initial_running_state = mock_session_state.research_progress.get("is_running", False)
    print(f"✅ Step 2: Initial session state synced - Running: {initial_running_state}")
    
    # Step 3: Start background thread
    thread_completed = False
    thread_error = None
    
    def run_pipeline_thread():
        nonlocal thread_completed, thread_error
        try:
            print("🚀 Background thread: Starting research pipeline...")
            run_research_pipeline_thread_safe(disease_name, country, time_period, num_works)
            thread_completed = True
            print("✅ Background thread: Research pipeline completed")
        except Exception as e:
            thread_error = e
            print(f"❌ Background thread: Research pipeline failed: {e}")
            import traceback
            traceback.print_exc()
    
    pipeline_thread = threading.Thread(target=run_pipeline_thread, daemon=True)
    pipeline_thread.start()
    
    print("✅ Step 3: Background thread started")
    
    # Step 4: Monitor progress with session state sync
    print("\n📊 Monitoring workflow progress...")
    
    max_monitor_time = 90  # 1.5 minutes
    start_time = time.time()
    sync_count = 0
    
    while time.time() - start_time < max_monitor_time:
        # Sync progress to session state (simulating Streamlit's behavior)
        sync_progress_to_session_state(mock_session_state)
        sync_count += 1
        
        # Check session state
        is_running = mock_session_state.research_progress.get("is_running", False)
        current_step = mock_session_state.research_progress.get("current_step", "Unknown")
        completed_steps = mock_session_state.research_progress.get("completed_steps", 0)
        total_steps = mock_session_state.research_progress.get("total_steps", 6)
        errors = mock_session_state.research_progress.get("errors", [])
        
        elapsed = time.time() - start_time
        print(f"Time: {elapsed:.1f}s - Sync #{sync_count} - Running: {is_running} - Step: {current_step} ({completed_steps}/{total_steps})")
        
        if errors:
            print(f"   Errors: {len(errors)} - Latest: {errors[-1] if errors else 'None'}")
        
        # Check if pipeline completed
        if not is_running and completed_steps > 0:
            print("✅ Pipeline completed successfully!")
            break
        
        # Check if thread died unexpectedly
        if not pipeline_thread.is_alive() and is_running:
            print("⚠️ Background thread died but session state still shows running")
            break
        
        time.sleep(2)
    
    # Wait for thread to complete
    pipeline_thread.join(timeout=10)
    
    print("✅ Step 4: Progress monitoring completed")
    
    # Step 5: Final state verification
    print("\n📋 Final Workflow State:")
    
    # Final sync
    sync_progress_to_session_state(mock_session_state)
    
    final_progress = mock_session_state.research_progress
    final_operations = mock_session_state.operation_status
    
    print(f"   Thread completed: {thread_completed}")
    print(f"   Thread error: {thread_error}")
    print(f"   Session running: {final_progress.get('is_running', False)}")
    print(f"   Current step: {final_progress.get('current_step', 'Unknown')}")
    print(f"   Completed steps: {final_progress.get('completed_steps', 0)}/{final_progress.get('total_steps', 6)}")
    print(f"   Results count: {len(final_progress.get('results', {}))}")
    print(f"   Errors count: {len(final_progress.get('errors', []))}")
    print(f"   Operations tracked: {len(final_operations)}")
    print(f"   Total syncs performed: {sync_count}")
    
    if final_progress.get('errors'):
        print("❌ Pipeline errors:")
        for error in final_progress['errors']:
            print(f"   - {error}")
    
    if final_progress.get('results'):
        print("✅ Pipeline results:")
        for step, result in final_progress['results'].items():
            print(f"   - {step}: {type(result).__name__}")
    
    # Determine success criteria
    success_criteria = [
        ("Thread completed", thread_completed),
        ("No thread errors", thread_error is None),
        ("Progress made", final_progress.get('completed_steps', 0) > 0),
        ("Session state synced", sync_count > 0),
        ("Results generated", len(final_progress.get('results', {})) > 0)
    ]
    
    print("\n🎯 Success Criteria:")
    all_passed = True
    for criterion, passed in success_criteria:
        status = "✅" if passed else "❌"
        print(f"   {status} {criterion}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 End-to-end workflow test PASSED!")
        return True
    else:
        print("\n❌ End-to-end workflow test FAILED!")
        return False

if __name__ == "__main__":
    print("🚀 Starting End-to-End Workflow Test")
    print("=" * 60)
    
    success = test_end_to_end_workflow()
    
    if success:
        print("\n🎉 End-to-end workflow test completed successfully!")
        print("\n📝 Summary:")
        print("   - Background thread execution: ✅ Working")
        print("   - Progress synchronization: ✅ Working") 
        print("   - Session state management: ✅ Working")
        print("   - Database thread safety: ✅ Working")
        print("   - Pipeline completion: ✅ Working")
        print("\n🚀 The 'Start Research' button should now work correctly!")
    else:
        print("\n❌ End-to-end workflow test failed!")
        sys.exit(1)
