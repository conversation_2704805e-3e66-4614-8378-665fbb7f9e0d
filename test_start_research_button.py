#!/usr/bin/env python3
"""
Final test to demonstrate that the "Start Research" button functionality is working correctly.
This test simulates the exact workflow that happens when a user clicks the button in Streamlit.
"""

import sys
import os
import threading
import time

# Add src directory to Python path
sys.path.append('src')

def test_start_research_button_workflow():
    """Test the complete Start Research button workflow."""
    
    print("🧪 Testing Start Research Button Workflow")
    print("=" * 50)
    
    # Import required modules
    from streamlit_app.utils.progress_manager import get_progress_manager, sync_progress_to_session_state
    from streamlit_app.pages.research_interface import start_research_process
    
    # Mock session state (simulating Streamlit's session state)
    class MockSessionState:
        def __init__(self):
            self.research_progress = {
                "is_running": False,
                "current_step": "Idle",
                "disease_name": None,
                "country": None,
                "total_steps": 6,
                "completed_steps": 0,
                "step_details": {},
                "results": {},
                "errors": [],
                "last_updated": None
            }
            self.operation_status = {}
            self.research_form_data = {}
        
        def get(self, key, default=None):
            return getattr(self, key, default)
        
        def update(self, data):
            for key, value in data.items():
                setattr(self, key, value)
    
    # Create mock session state
    mock_session_state = MockSessionState()
    
    # Test parameters (simulating user input)
    disease_name = "hypertension"
    country = "US"
    time_period = "2020-2025"
    num_works = 3
    screening_workers = 5
    download_workers = 10
    extraction_workers = 20
    
    print(f"Simulating user input:")
    print(f"   Disease: {disease_name}")
    print(f"   Country: {country}")
    print(f"   Time Period: {time_period}")
    print(f"   Number of Works: {num_works}")
    
    # Step 1: Simulate clicking "Start Research" button
    print("\n🚀 Step 1: Simulating 'Start Research' button click...")
    
    try:
        # This is exactly what happens when the button is clicked
        start_research_process(
            disease_name,
            country,
            time_period,
            num_works,
            screening_workers,
            download_workers,
            extraction_workers
        )
        print("✅ Start research process initiated successfully")
    except Exception as e:
        print(f"❌ Failed to start research process: {e}")
        return False
    
    # Step 2: Simulate Streamlit's page refresh and progress checking
    print("\n📊 Step 2: Simulating Streamlit page refresh and progress monitoring...")
    
    max_monitor_time = 90  # 1.5 minutes
    start_time = time.time()
    refresh_count = 0
    
    while time.time() - start_time < max_monitor_time:
        # Simulate what happens on each Streamlit page refresh
        refresh_count += 1
        
        # Sync progress from background thread to session state
        sync_progress_to_session_state(mock_session_state)
        
        # Check if research is running (this determines if we show progress or form)
        is_running = mock_session_state.research_progress.get("is_running", False)
        current_step = mock_session_state.research_progress.get("current_step", "Unknown")
        completed_steps = mock_session_state.research_progress.get("completed_steps", 0)
        total_steps = mock_session_state.research_progress.get("total_steps", 6)
        errors = mock_session_state.research_progress.get("errors", [])
        
        elapsed = time.time() - start_time
        
        # Simulate what the UI would show
        if is_running:
            ui_state = "PROGRESS VIEW"
        else:
            ui_state = "FORM VIEW" if completed_steps == 0 else "COMPLETED VIEW"
        
        print(f"Refresh #{refresh_count} ({elapsed:.1f}s): UI={ui_state} | Running={is_running} | Step='{current_step}' | Progress={completed_steps}/{total_steps}")
        
        if errors:
            print(f"   ⚠️ Errors: {len(errors)} - Latest: {errors[-1]}")
        
        # Check completion
        if not is_running and completed_steps > 0:
            print("✅ Research pipeline completed!")
            break
        
        # Simulate Streamlit's refresh interval
        time.sleep(2)
    
    # Step 3: Final state verification
    print("\n📋 Step 3: Final state verification...")
    
    # Final sync
    sync_progress_to_session_state(mock_session_state)
    
    final_progress = mock_session_state.research_progress
    final_operations = mock_session_state.operation_status
    
    print(f"Final State:")
    print(f"   UI would show: {'PROGRESS VIEW' if final_progress.get('is_running') else 'COMPLETED VIEW'}")
    print(f"   Research running: {final_progress.get('is_running', False)}")
    print(f"   Current step: {final_progress.get('current_step', 'Unknown')}")
    print(f"   Progress: {final_progress.get('completed_steps', 0)}/{final_progress.get('total_steps', 6)}")
    print(f"   Results generated: {len(final_progress.get('results', {}))}")
    print(f"   Errors: {len(final_progress.get('errors', []))}")
    print(f"   Total UI refreshes: {refresh_count}")
    
    # Show results
    if final_progress.get('results'):
        print(f"\n✅ Pipeline Results:")
        for step, result in final_progress['results'].items():
            print(f"   - {step}: {type(result).__name__}")
    
    # Show errors (if any)
    if final_progress.get('errors'):
        print(f"\n⚠️ Pipeline Errors:")
        for error in final_progress['errors']:
            print(f"   - {error}")
    
    # Determine success
    success_criteria = [
        ("Research was initiated", True),  # We got this far
        ("Progress was made", final_progress.get('completed_steps', 0) > 0),
        ("UI refreshes worked", refresh_count > 0),
        ("Results were generated", len(final_progress.get('results', {})) > 0),
        ("Pipeline completed", not final_progress.get('is_running', True))
    ]
    
    print(f"\n🎯 Success Criteria:")
    all_passed = True
    for criterion, passed in success_criteria:
        status = "✅" if passed else "❌"
        print(f"   {status} {criterion}")
        if not passed:
            all_passed = False
    
    return all_passed

if __name__ == "__main__":
    print("🚀 Testing Start Research Button Functionality")
    print("=" * 60)
    print("This test simulates exactly what happens when a user:")
    print("1. Fills out the research form")
    print("2. Clicks the 'Start Research' button")
    print("3. Watches the progress updates in real-time")
    print("4. Sees the final results")
    print("=" * 60)
    
    success = test_start_research_button_workflow()
    
    if success:
        print("\n🎉 START RESEARCH BUTTON TEST PASSED!")
        print("\n✅ The regression issue has been RESOLVED!")
        print("\n📝 Expected Behavior:")
        print("   1. User clicks 'Start Research' button")
        print("   2. UI immediately switches to progress view")
        print("   3. Progress updates continuously in real-time")
        print("   4. UI stays in progress view until completion")
        print("   5. Results are displayed when finished")
        print("\n🚀 The epidemiology research system is now fully functional!")
    else:
        print("\n❌ START RESEARCH BUTTON TEST FAILED!")
        print("The regression issue still exists.")
        sys.exit(1)
