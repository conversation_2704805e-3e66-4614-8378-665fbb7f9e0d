# Epidemiology Research System

A comprehensive system for automated retrieval, screening, and analysis of epidemiological research articles. This system integrates with OpenAlex for article discovery, implements AI-powered screening for epidemiology relevance, and manages full-text article processing with MySQL database storage.

## Features

- **Automated Article Discovery**: Search and retrieve articles from OpenAlex API based on disease keywords
- **Disease Validation**: AI-powered validation of disease names with suggested corrections
- **Intelligent Screening**: Automated screening of articles for epidemiological relevance using AI agents
- **Full-text Processing**: Download and process full-text articles from PubMed Central
- **Database Management**: Complete MySQL database integration for persistent storage
- **Progress Tracking**: Real-time progress tracking with comprehensive statistics

## Architecture

The system consists of several key components:

### Core Modules

- **`src/handlers/`**: External API handlers (OpenAlex, PubMed)
- **`src/agents/`**: AI-powered agents for disease validation and article screening
- **`src/db/`**: Database connection management and article operations
- **`src/helpers/`**: Utility functions and data models

### Database Schema

The system uses a MySQL database with the following main table:

#### `dim_articles`
- **Primary Keys**: `pmid` (PubMed ID)
- **Identifiers**: `pmcid` (PubMed Central ID)
- **Article Metadata**: `title`, `abstract`, `publication_type`, `publication_year`, `pdf_url`
- **Screening Results**: `is_relevant` (boolean flag for epidemiology relevance)
- **Full-text Status**: `fulltext_status`, `fulltext_path`
- **Timestamps**: `created_at`, `updated_at`

## Installation

### Prerequisites

- Python 3.11+
- MySQL 8.0+
- Virtual environment support (uv recommended)

### Setup

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd epidemiology_research_system
   ```

2. **Install dependencies**:
   ```bash
   uv sync
   ```

3. **MySQL Installation and Configuration**:

   **Install MySQL Server on Ubuntu:**
   ```bash
   # Update package lists
   sudo apt update && sudo apt upgrade -y
   
   # Install MySQL server
   sudo apt install mysql-server -y
   
   # Check MySQL service status
   sudo systemctl status mysql
   ```

   **Configure MySQL Database and User:**
   ```bash
   # Connect to MySQL as root (no password initially)
   sudo mysql
   ```

   In the MySQL prompt, run the following commands:
   ```sql
   -- Create the epidemiology_research database
   CREATE DATABASE IF NOT EXISTS epidemiology_research;
   
   -- Create a dedicated user for the application
   CREATE USER IF NOT EXISTS 'epidemiology_user'@'localhost' IDENTIFIED BY 'EpiResearch2024!';
   
   -- Grant all privileges on the epidemiology_research database to the user
   GRANT ALL PRIVILEGES ON epidemiology_research.* TO 'epidemiology_user'@'localhost';
   
   -- Set root password (optional, for better security)
   ALTER USER 'root'@'localhost' IDENTIFIED BY 'RootPass2024!';
   
   -- Flush privileges to ensure all changes take effect
   FLUSH PRIVILEGES;
   
   -- Exit MySQL
   exit;
   ```

   **Test the Database Connection:**
   ```bash
   # Test connection with the new user
   mysql -u epidemiology_user -p'EpiResearch2024!' -e "USE epidemiology_research; SHOW TABLES;"
   ```

4. **Database Configuration**:
   
   Create a `.env` file in the project root with your database credentials:
   ```bash
   # Database Configuration
   MYSQL_HOST=localhost
   MYSQL_PORT=3306
   MYSQL_DATABASE=epidemiology_research
   MYSQL_USERNAME=epidemiology_user
   MYSQL_PASSWORD=EpiResearch2024!
   MYSQL_ECHO=false
   ```
   
   See `.db_config` for the complete configuration template with both user and root credentials.

## Usage

### Command Line Interface

Run the main CLI application:

```bash
python cli.py
```

The CLI will guide you through the following steps:

1. **Input Parameters**:
   - Disease name (default: "ulcerative colitis")
   - Country code (default: "US") 
   - Time period (default: "1970-2025")
   - Number of works to retrieve (default: 50)

2. **Processing Pipeline**:
   - ✅ Database connection and table creation
   - 🔍 Disease name validation
   - 📚 Article search and retrieval from OpenAlex
   - 💾 Save articles to database
   - 🔍 AI-powered epidemiology screening
   - 📊 Update screening results in database
   - 📄 Full-text article download
   - 📊 Update full-text status in database
   - 📈 Display comprehensive statistics

### Database Operations

The system provides comprehensive database operations through the `ArticleManager` class:

```python
from src.db import DatabaseConnection, ArticleManager

# Initialize database connection
db_connection = DatabaseConnection()
db_connection.connect()
db_connection.create_tables()

# Create article manager
article_manager = ArticleManager(db_connection)

# Get articles by criteria
relevant_articles = article_manager.get_articles_by_criteria(
    is_relevant=True, 
    fulltext_status='SUCCESS'
)

# Get database statistics
stats = article_manager.get_article_statistics()
```

## Data Flow

1. **Article Discovery**: Search OpenAlex API for articles matching disease keywords and criteria
2. **Database Storage**: Save article metadata to `dim_articles` table
3. **AI Screening**: Process articles through disease epidemiology screening agent
4. **Screening Updates**: Update database with relevance flags
5. **Full-text Processing**: Download full-text articles from PubMed Central
6. **Status Updates**: Update database with download status and file paths

## Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `MYSQL_HOST` | MySQL server hostname | localhost | Yes |
| `MYSQL_PORT` | MySQL server port | 3306 | No |
| `MYSQL_DATABASE` | Database name | epidemiology_research | Yes |
| `MYSQL_USERNAME` | Database username | epidemiology_user | Yes |
| `MYSQL_PASSWORD` | Database password | EpiResearch2024! | Yes |
| `MYSQL_ECHO` | Enable SQL logging | false | No |

### Agent Configuration

AI agent settings are configured in `src/config.json`:

- **DiseaseGuardrailAgent**: Disease name validation
- **DiseaseAbstractScreeningAgent**: Epidemiology relevance screening
- **PrevalenceExtractionAgent**: Prevalence data extraction
- **IncidenceExtractionAgent**: Incidence data extraction

## Development

### Code Style

The project follows Python development standards:

- **Black** formatting (88 character line length)
- **Type hints** for all functions
- **Comprehensive docstrings** (Google style)
- **PEP 8** compliance

### Testing

Run tests using pytest:

```bash
pytest tests/
```

### Database Migrations

The system automatically creates database tables on first run. For schema changes, the `create_tables()` method will handle table updates.

### Database Management Scripts

Located in `src/db/scripts/`, these tools provide complete database management:

**Complete Database Reset:**
```bash
# Interactive mode with double confirmation
src/db/scripts/purge_db.sh

# Force mode (USE WITH EXTREME CAUTION!)
src/db/scripts/purge_db.sh --force
```

⚠️ **WARNING**: These scripts completely destroy and recreate the database, including all structure and data. Use only for development, testing, or when complete reset is required.

## File Structure

```
epidemiology_research_system/
├── cli.py                    # Main CLI application
├── src/                      # Source code
│   ├── db/                   # Database modules
│   │   ├── __init__.py
│   │   ├── connection.py     # Database connection management
│   │   ├── models.py         # SQLAlchemy models
│   │   ├── article_manager.py # Article CRUD operations
│   │   └── scripts/          # Database management scripts
│   │       ├── purge_database.sql # Complete database reset SQL
│   │       ├── purge_database.py  # Database reset Python script
│   │       ├── purge_db.sh       # Database reset shell script
│   │       └── README.md         # Database scripts documentation
│   ├── handlers/             # External API handlers
│   ├── agents/               # AI agents
│   └── helpers/              # Utility functions
├── data/                     # Data storage
│   └── publications/         # Downloaded full-text articles
├── examples/                 # Example scripts
├── database_config.example   # Database configuration template
└── pyproject.toml           # Project configuration
```

## Contributing

1. Follow the Python development standards outlined in the project
2. Write comprehensive tests for new functionality
3. Update documentation for new features
4. Ensure database migrations are handled properly

## License

[Add your license information here]

## Support

For issues and questions:
1. Check the database configuration in `.db_config`
2. Verify MySQL connectivity and permissions
3. Review logs for detailed error information
4. Ensure all dependencies are installed correctly

### MySQL Troubleshooting

**Common MySQL Issues:**

1. **Service not running:**
   ```bash
   sudo systemctl start mysql
   sudo systemctl enable mysql
   ```

2. **Connection denied:**
   ```bash
   # Check if user exists and has correct permissions
   sudo mysql -e "SELECT User, Host FROM mysql.user WHERE User='epidemiology_user';"
   
   # Verify database exists
   sudo mysql -e "SHOW DATABASES LIKE 'epidemiology_research';"
   ```

3. **Password authentication failed:**
   ```bash
   # Reset user password if needed
   sudo mysql -e "ALTER USER 'epidemiology_user'@'localhost' IDENTIFIED BY 'EpiResearch2024!';"
   ```

4. **Check MySQL error logs:**
   ```bash
   sudo tail -f /var/log/mysql/error.log
   ```
