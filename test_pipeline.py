#!/usr/bin/env python3
"""
Test script to verify the research pipeline works correctly.
This script tests the core pipeline functionality outside of Streamlit.
"""

import sys
import os
import json
from pathlib import Path

# Add src directory to Python path
sys.path.append('src')

def test_pipeline_components():
    """Test individual pipeline components."""
    
    print("🧪 Testing Pipeline Components")
    print("=" * 50)
    
    # Test 1: Config loading
    try:
        with open("src/config.json", "r") as f:
            config = json.load(f)
        print("✅ Config loading: SUCCESS")
        infra_config = config["InfrastructureParameters"]
        print(f"   Workers config: {infra_config}")
    except Exception as e:
        print(f"❌ Config loading: FAILED - {e}")
        return False
    
    # Test 2: Database connection
    try:
        from src.db import DatabaseConnection, ArticleManager, ExtractionManager
        db_connection = DatabaseConnection()
        db_connection.connect()
        db_connection.create_tables()
        
        article_manager = ArticleManager(db_connection)
        extraction_manager = ExtractionManager(db_connection)
        print("✅ Database connection: SUCCESS")
    except Exception as e:
        print(f"❌ Database connection: FAILED - {e}")
        return False
    
    # Test 3: Disease validation
    try:
        from src.agents.disease_guardrail import validate_disease_name
        result = validate_disease_name("ulcerative colitis")
        if result["is_disease"]:
            print("✅ Disease validation: SUCCESS")
        else:
            print(f"❌ Disease validation: FAILED - {result['explanation']}")
            return False
    except Exception as e:
        print(f"❌ Disease validation: FAILED - {e}")
        return False
    
    # Test 4: OpenAlex client
    try:
        from src.handlers.openalex_handler import OpenAlexClient
        oaclient = OpenAlexClient(apply_topic_filtering=False)
        search_query = oaclient.construct_search_query(["ulcerative colitis"])
        print("✅ OpenAlex client: SUCCESS")
        print(f"   Sample query: {search_query}")
    except Exception as e:
        print(f"❌ OpenAlex client: FAILED - {e}")
        return False
    
    print("\n🎉 All component tests passed!")
    return True

def test_minimal_pipeline():
    """Test a minimal version of the research pipeline."""
    
    print("\n🔬 Testing Minimal Pipeline")
    print("=" * 50)
    
    try:
        # Initialize components
        from src.db import DatabaseConnection, ArticleManager, ExtractionManager
        from src.agents.disease_guardrail import validate_disease_name
        from src.handlers.openalex_handler import OpenAlexClient
        
        # Load config
        with open("src/config.json", "r") as f:
            infra_config = json.load(f)["InfrastructureParameters"]
        
        # Setup database
        db_connection = DatabaseConnection()
        db_connection.connect()
        db_connection.create_tables()
        article_manager = ArticleManager(db_connection)
        extraction_manager = ExtractionManager(db_connection)
        
        # Test parameters
        disease_name = "ulcerative colitis"
        country = "US"
        time_period = "2020-2025"
        num_works = 5  # Small number for testing
        
        print(f"Testing with: {disease_name}, {country}, {time_period}, {num_works} works")
        
        # Step 1: Validate disease
        print("Step 1: Validating disease name...")
        validation_result = validate_disease_name(disease_name)
        if not validation_result["is_disease"]:
            print(f"❌ Disease validation failed: {validation_result['explanation']}")
            return False
        print("✅ Disease validation passed")
        
        # Step 2: Search for articles (limited)
        print("Step 2: Searching for articles...")
        existing_count = article_manager.count_existing_articles_for_disease(disease_name)
        remaining_to_search = max(0, num_works - existing_count)
        
        if remaining_to_search > 0:
            oaclient = OpenAlexClient(apply_topic_filtering=False)
            search_query = oaclient.construct_search_query([disease_name])
            publications = oaclient.search_works(
                search_query=search_query,
                country=country,
                time_period=time_period,
                num_works=remaining_to_search,
            )
            print(f"✅ Found {len(publications)} articles")
        else:
            print(f"✅ Already have {existing_count} articles for {disease_name}")
        
        print("🎉 Minimal pipeline test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting Pipeline Tests")
    print("=" * 60)
    
    # Test components first
    if not test_pipeline_components():
        print("\n❌ Component tests failed. Exiting.")
        sys.exit(1)
    
    # Test minimal pipeline
    if not test_minimal_pipeline():
        print("\n❌ Pipeline test failed. Exiting.")
        sys.exit(1)
    
    print("\n🎉 All tests passed! Pipeline is working correctly.")
