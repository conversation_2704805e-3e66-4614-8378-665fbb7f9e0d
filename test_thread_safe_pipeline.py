#!/usr/bin/env python3
"""
Test script to verify the thread-safe research pipeline works correctly.
"""

import sys
import os
import threading
import time

# Add src directory to Python path
sys.path.append('src')

def test_thread_safe_pipeline():
    """Test the thread-safe research pipeline."""
    
    print("🧪 Testing Thread-Safe Research Pipeline")
    print("=" * 50)
    
    # Import the progress manager and pipeline function
    from streamlit_app.utils.progress_manager import get_progress_manager
    from streamlit_app.pages.research_interface import run_research_pipeline_thread_safe
    
    # Get progress manager
    progress_manager = get_progress_manager()
    progress_manager.reset_progress()
    
    # Test parameters
    disease_name = "ulcerative colitis"
    country = "US"
    time_period = "2020-2025"
    num_works = 3  # Small number for testing
    
    print(f"Testing pipeline with: {disease_name}, {country}, {time_period}, {num_works} works")
    
    # Set initial state
    progress_manager.set_running(disease_name, country, total_steps=6)
    
    print(f"Initial state: {progress_manager.get_progress()}")
    
    # Test running the pipeline in a thread
    def run_pipeline_thread():
        try:
            print("🚀 Starting thread-safe pipeline...")
            run_research_pipeline_thread_safe(disease_name, country, time_period, num_works)
            print("✅ Thread-safe pipeline completed!")
        except Exception as e:
            print(f"❌ Thread-safe pipeline failed: {e}")
            import traceback
            traceback.print_exc()
    
    # Start the pipeline thread
    pipeline_thread = threading.Thread(target=run_pipeline_thread, daemon=True)
    pipeline_thread.start()
    
    # Monitor progress for a while
    print("\n📊 Monitoring progress...")
    for i in range(60):  # Monitor for 60 seconds max
        progress = progress_manager.get_progress()
        print(f"Step {i+1}: {progress['current_step']} ({progress['completed_steps']}/{progress['total_steps']})")
        
        if not progress['is_running']:
            print("✅ Pipeline completed!")
            break
            
        if progress['errors']:
            print(f"❌ Errors detected: {progress['errors']}")
            break
            
        time.sleep(1)
    
    # Wait for thread to complete
    pipeline_thread.join(timeout=10)
    
    # Final state
    final_state = progress_manager.get_progress()
    print(f"\n📋 Final state:")
    print(f"   Running: {final_state['is_running']}")
    print(f"   Current step: {final_state['current_step']}")
    print(f"   Completed steps: {final_state['completed_steps']}/{final_state['total_steps']}")
    print(f"   Errors: {len(final_state['errors'])}")
    print(f"   Results: {list(final_state['results'].keys())}")
    
    if final_state['errors']:
        print("❌ Errors occurred:")
        for error in final_state['errors']:
            print(f"   - {error}")
        return False
    
    if final_state['completed_steps'] > 0:
        print("✅ Pipeline made progress!")
        return True
    else:
        print("❌ Pipeline didn't make any progress")
        return False

if __name__ == "__main__":
    print("🚀 Starting Thread-Safe Pipeline Test")
    print("=" * 60)
    
    success = test_thread_safe_pipeline()
    
    if success:
        print("\n🎉 Thread-safe pipeline test completed successfully!")
    else:
        print("\n❌ Thread-safe pipeline test failed!")
        sys.exit(1)
