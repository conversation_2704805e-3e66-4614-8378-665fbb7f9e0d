# Streamlit Epidemiology Research Application

A comprehensive web application that replaces the CLI interface with an interactive user experience for epidemiological research.

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- UV package manager
- MySQL database (optional - sample data available)

### Installation & Running

1. **Install dependencies:**
   ```bash
   uv add streamlit plotly pandas
   ```

2. **Run the application:**
   ```bash
   uv run streamlit run app.py
   ```

3. **Access the application:**
   - Local: http://localhost:8501
   - Network: http://[your-ip]:8501

## 📋 Features

### 🔍 Research Interface
- **Disease Research**: Conduct epidemiological research with real-time progress tracking
- **Configuration Options**: Customize worker counts for parallel processing
- **Progress Monitoring**: Real-time updates during article screening, download, and extraction
- **Form Validation**: Input validation with helpful error messages
- **Session Persistence**: Research progress maintained across page refreshes

### 📊 Data Dashboard
- **Interactive Visualizations**: Charts and graphs for prevalence and incidence data
- **Advanced Filtering**: Filter by disease, country, and data type
- **Sample Data Mode**: Demo functionality without database connection
- **Data Tables**: Detailed tabular views of extracted data
- **Summary Statistics**: Overview of available data records

### 🧭 Navigation & UX
- **Intuitive Navigation**: Easy switching between research and dashboard pages
- **Session State Management**: Maintains user selections and progress
- **Error Handling**: Comprehensive error messages and recovery suggestions
- **Responsive Design**: Works on desktop and mobile devices
- **Help System**: Built-in help and tips for users

## 🏗️ Architecture

### Project Structure
```
streamlit_app/
├── components/          # Reusable UI components
│   ├── __init__.py
│   └── progress_tracker.py
├── pages/              # Main application pages
│   ├── __init__.py
│   ├── research_interface.py
│   └── data_dashboard.py
└── utils/              # Utility modules
    ├── __init__.py
    ├── database.py
    ├── sample_data.py
    └── session_state.py
```

### Key Components

#### 1. **Main Application** (`app.py`)
- Entry point with navigation and database connection checking
- Session state initialization
- Page routing and sidebar management

#### 2. **Research Interface** (`streamlit_app/pages/research_interface.py`)
- Replicates CLI functionality with web interface
- Real-time progress tracking during research operations
- Form-based configuration with validation
- Background research pipeline execution

#### 3. **Data Dashboard** (`streamlit_app/pages/data_dashboard.py`)
- Interactive data visualization using Plotly
- Advanced filtering and data exploration
- Sample data support for demonstration
- Responsive charts and tables

#### 4. **Database Utilities** (`streamlit_app/utils/database.py`)
- Database connection management with caching
- Data retrieval functions for prevalence and incidence
- Error handling and connection status monitoring

#### 5. **Session State Management** (`streamlit_app/utils/session_state.py`)
- Centralized session state management
- Research progress tracking
- Filter state persistence
- Operation status updates

#### 6. **Sample Data Generator** (`streamlit_app/utils/sample_data.py`)
- Generates realistic sample data for testing
- Supports all data types (prevalence, incidence)
- Configurable data volumes and parameters

## 🔧 Configuration

### Database Connection
The application automatically detects database connectivity:
- **Connected**: Full functionality with real data
- **Disconnected**: Limited functionality with sample data option

### Environment Variables
Configure database connection via environment variables or `.env` file:
```bash
DB_HOST=localhost
DB_PORT=3306
DB_NAME=epidemiology_db
DB_USER=your_username
DB_PASSWORD=your_password
```

## 🧪 Testing

Run the test suite to validate functionality:
```bash
uv run python test_streamlit_app.py
```

The test suite validates:
- ✅ File structure integrity
- ✅ Module imports
- ✅ Sample data generation
- ✅ Database connectivity
- ✅ Session state management

## 📊 Sample Data

When database is unavailable, the application provides rich sample data:
- **100 point prevalence records**
- **80 period prevalence records**
- **60 incidence rate records**
- **50 cumulative incidence records**
- **5 sample diseases** (Type 2 Diabetes, Hypertension, etc.)
- **8 sample countries** (US, UK, CA, AU, DE, FR, JP, IN)

## 🎯 Usage Examples

### Conducting Research
1. Navigate to **Research Interface**
2. Enter disease name (e.g., "Type 2 Diabetes")
3. Specify country code (e.g., "US")
4. Set time period (e.g., "2020-2024")
5. Configure worker counts if needed
6. Click **Start Research**
7. Monitor real-time progress

### Exploring Data
1. Navigate to **Data Dashboard**
2. Use filters to select diseases/countries
3. Choose data type (prevalence, incidence, or all)
4. Explore interactive charts and tables
5. Export or analyze filtered results

## 🔍 Troubleshooting

### Common Issues

**Database Connection Failed**
- Check environment variables
- Verify database server is running
- Use sample data mode for testing

**Import Errors**
- Ensure all dependencies are installed: `uv add streamlit plotly pandas`
- Check Python version compatibility

**Performance Issues**
- Reduce worker counts in research configuration
- Clear browser cache
- Restart Streamlit application

### Getting Help
- Use the built-in help system in the sidebar
- Check error messages for specific guidance
- Review debug information in error expandable sections

## 🚀 Deployment

### Local Development
```bash
uv run streamlit run app.py
```

### Production Deployment
```bash
uv run streamlit run app.py --server.port 8501 --server.address 0.0.0.0
```

## 📈 Performance

- **Caching**: Database queries cached for 60 seconds
- **Parallel Processing**: Configurable worker counts for research operations
- **Lazy Loading**: Data loaded on-demand with filtering
- **Session Persistence**: State maintained across page refreshes

## 🔒 Security

- Input validation on all forms
- SQL injection protection via ORM
- Session state isolation
- Error message sanitization

---

**Built with ❤️ using Streamlit, Plotly, and modern Python practices**
