#!/usr/bin/env python3
"""
Test script to simulate the Streamlit research pipeline execution.
This tests the threading and session state logic.
"""

import sys
import os
import json
import threading
import time
from unittest.mock import MagicMock

# Add src directory to Python path
sys.path.append('src')

# Mock Streamlit session state
class MockSessionState:
    def __init__(self):
        self.data = {}
        
    def __getitem__(self, key):
        return self.data[key]
    
    def __setitem__(self, key, value):
        self.data[key] = value
    
    def get(self, key, default=None):
        return self.data.get(key, default)
    
    def update(self, other):
        self.data.update(other)

def test_streamlit_pipeline():
    """Test the Streamlit pipeline execution with mocked session state."""
    
    print("🧪 Testing Streamlit Pipeline Execution")
    print("=" * 50)
    
    # Mock session state
    mock_session_state = MockSessionState()
    
    # Initialize session state like Streamlit does
    mock_session_state.data = {
        "research_progress": {
            "is_running": False,
            "current_step": "Idle",
            "disease_name": None,
            "country": None,
            "total_steps": 0,
            "completed_steps": 0,
            "step_details": {},
            "results": {},
            "errors": []
        },
        "worker_config": {},
        "operation_status": {
            "screening": {"total": 0, "completed": 0, "status": "idle"},
            "download": {"total": 0, "completed": 0, "status": "idle"},
            "extraction": {"total": 0, "completed": 0, "status": "idle"}
        }
    }
    
    # Import and mock the session state functions
    from streamlit_app.utils.session_state import (
        update_research_progress,
        update_operation_status,
        set_research_running,
        set_research_completed,
        add_research_error,
        reset_research_progress
    )
    
    # Mock streamlit module
    import streamlit as st
    st.session_state = mock_session_state
    
    # Import the research interface functions
    from streamlit_app.pages.research_interface import run_research_pipeline
    
    # Test parameters
    disease_name = "ulcerative colitis"
    country = "US"
    time_period = "2020-2025"
    num_works = 3  # Very small for testing
    
    print(f"Testing pipeline with: {disease_name}, {country}, {time_period}, {num_works} works")
    
    # Reset and set running state
    reset_research_progress()
    set_research_running(disease_name, country, total_steps=6)
    
    print(f"Initial state: {mock_session_state.data['research_progress']}")
    
    # Test running the pipeline in a thread
    def run_pipeline_thread():
        try:
            print("🚀 Starting pipeline in thread...")
            run_research_pipeline(disease_name, country, time_period, num_works)
            print("✅ Pipeline completed successfully!")
        except Exception as e:
            print(f"❌ Pipeline failed: {e}")
            import traceback
            traceback.print_exc()
    
    # Start the pipeline thread
    pipeline_thread = threading.Thread(target=run_pipeline_thread, daemon=True)
    pipeline_thread.start()
    
    # Monitor progress for a while
    print("\n📊 Monitoring progress...")
    for i in range(30):  # Monitor for 30 seconds max
        progress = mock_session_state.data['research_progress']
        print(f"Step {i+1}: {progress['current_step']} ({progress['completed_steps']}/{progress['total_steps']})")
        
        if not progress['is_running']:
            print("✅ Pipeline completed!")
            break
            
        if progress['errors']:
            print(f"❌ Errors detected: {progress['errors']}")
            break
            
        time.sleep(1)
    
    # Wait for thread to complete
    pipeline_thread.join(timeout=5)
    
    # Final state
    final_state = mock_session_state.data['research_progress']
    print(f"\n📋 Final state:")
    print(f"   Running: {final_state['is_running']}")
    print(f"   Current step: {final_state['current_step']}")
    print(f"   Completed steps: {final_state['completed_steps']}/{final_state['total_steps']}")
    print(f"   Errors: {len(final_state['errors'])}")
    print(f"   Results: {list(final_state['results'].keys())}")
    
    if final_state['errors']:
        print("❌ Errors occurred:")
        for error in final_state['errors']:
            print(f"   - {error}")
    
    return len(final_state['errors']) == 0

if __name__ == "__main__":
    print("🚀 Starting Streamlit Pipeline Test")
    print("=" * 60)
    
    success = test_streamlit_pipeline()
    
    if success:
        print("\n🎉 Streamlit pipeline test completed successfully!")
    else:
        print("\n❌ Streamlit pipeline test failed!")
        sys.exit(1)
