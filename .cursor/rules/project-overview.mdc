# Epidemiology Research System - Project Overview

This is a Python-based epidemiology research system designed for analyzing and modeling epidemiological data.

## Project Structure

- **Main Entry Point**: [main.py](mdc:main.py) - Contains the primary application entry point
- **Configuration**: [pyproject.toml](mdc:pyproject.toml) - Project configuration and dependencies
- **Python Version**: [.python-version](mdc:.python-version) - Specifies Python 3.11+ requirement
- **Documentation**: [README.md](mdc:README.md) - Project documentation
- **Examples**: [examples/](mdc:examples/) - Example implementations and usage patterns

## Key Characteristics

- **Language**: Python 3.11+
- **Package Management**: Uses pyproject.toml for modern Python packaging
- **Focus**: Epidemiology research, data analysis, and modeling
- **Development**: Early stage project with basic structure

## Development Guidelines

- Follow Python 3.11+ syntax and features
- Use type hints for better code documentation
- Implement proper error handling for data processing
- Follow epidemiological research best practices
- Ensure reproducible research workflows
