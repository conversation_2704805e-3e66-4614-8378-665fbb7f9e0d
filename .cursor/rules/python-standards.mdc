---
description: 
globs: 
alwaysApply: true
---
# Python Development Standards

## Code Style and Formatting

- Use **Black** for code formatting with line length of 88 characters
- Follow **PEP 8** style guidelines
- Use **isort** for import organization
- Enable **flake8** for linting

## Type Hints and Documentation

- **Always** use type hints for function parameters and return values
- Use `typing` module for complex types (List, Dict, Optional, Union)
- Write comprehensive docstrings using Google style format
- Include type information in docstrings for complex data structures

## Error Handling

- Use specific exception types rather than bare `except:` clauses
- Implement proper logging using Python's `logging` module
- Provide meaningful error messages in all modules.

## Testing Requirements

- Write unit tests for only `src` modules and scripts
- Do not write tests for scripts in  `examples` folder
- Create unit test sinside `tests` folder only.
- Use **pytest** for testing framework

## Dependencies

- Keep dependencies minimal and well-documented but don't create documentation for every user request and code snippet generation. 
- Always **ask** user after some development if he needs documentation for completing the update. Only generate documentation after user explictly requests it.
- Do not generate example usage scripts unless user explictly requests it.
- Use virtual environment `.venv` in current workspace for running any python commands. This project is managed by uv python package manager.
    - use `uv pip install <package-name>` to install package in virtual environment.
    - use `uv add <package-name> --dev` to add development dependencies and `uv add <package-name> --raw` to add general dependency in `pyproject.toml`

