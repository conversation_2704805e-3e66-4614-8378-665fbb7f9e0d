# Development Workflow and Best Practices

## Getting Started

1. **Environment Setup**:
   - Use Python 3.11+ as specified in [.python-version](mdc:.python-version)
   - Create virtual environment: `python -m venv .venv`
   - Activate environment: `source .venv/bin/activate`
   - Install dependencies: `pip install -e .`

2. **Development Process**:
   - Start with [main.py](mdc:main.py) for new features
   - Create modules in appropriate directories
   - Write tests for all epidemiological calculations
   - Document research methodology clearly

## Code Quality Checks

- **Formatting**: Run `black .` before committing
- **Linting**: Use `flake8` for code quality
- **Type Checking**: Use `mypy` for type validation
- **Testing**: Run `pytest` for all tests

## Research Data Handling

- **Data Privacy**: Never commit sensitive health data
- **Data Validation**: Always validate epidemiological data
- **Reproducibility**: Use deterministic random seeds
- **Documentation**: Document all data transformations

## Version Control

- Use meaningful commit messages for research changes
- Tag releases for reproducible research
- Keep [.gitignore](mdc:.gitignore) updated for Python files
- Never commit large datasets to git

## Testing Strategy

- **Unit Tests**: Test individual epidemiological functions
- **Integration Tests**: Test data processing pipelines
- **Validation Tests**: Compare against known epidemiological results
- **Performance Tests**: Ensure calculations complete in reasonable time

## Documentation Requirements

- Update [README.md](mdc:README.md) with new features
- Document epidemiological methods and assumptions
- Include usage examples for complex calculations
- Maintain API documentation for all public functions
