#!/usr/bin/env python3
"""
Test script to verify the threading fix for database managers.
"""

import sys
import os
import threading
import time

# Add src directory to Python path
sys.path.append('src')

def test_thread_safe_managers():
    """Test that the thread-safe database managers work correctly."""
    
    print("🧪 Testing Thread-Safe Database Managers")
    print("=" * 50)
    
    # Test 1: Regular managers (should work in main thread)
    try:
        from streamlit_app.utils.database import get_article_manager_for_thread, get_extraction_manager_for_thread
        
        print("Testing thread-safe managers in main thread...")
        article_manager = get_article_manager_for_thread()
        extraction_manager = get_extraction_manager_for_thread()
        
        if article_manager and extraction_manager:
            print("✅ Thread-safe managers work in main thread")
        else:
            print("❌ Thread-safe managers failed in main thread")
            return False
            
    except Exception as e:
        print(f"❌ Thread-safe manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test 2: Managers in background thread
    success = [False]  # Use list to modify from inner function
    error_msg = [None]
    
    def test_in_thread():
        try:
            print("Testing thread-safe managers in background thread...")
            article_manager = get_article_manager_for_thread()
            extraction_manager = get_extraction_manager_for_thread()
            
            if article_manager and extraction_manager:
                print("✅ Thread-safe managers work in background thread")
                
                # Test basic operations
                count = article_manager.count_existing_articles_for_disease("test_disease")
                print(f"✅ Article count query works: {count}")
                
                success[0] = True
            else:
                print("❌ Thread-safe managers failed in background thread")
                error_msg[0] = "Managers returned None"
                
        except Exception as e:
            print(f"❌ Background thread test failed: {e}")
            error_msg[0] = str(e)
            import traceback
            traceback.print_exc()
    
    # Run test in background thread
    thread = threading.Thread(target=test_in_thread, daemon=True)
    thread.start()
    thread.join(timeout=10)
    
    if not success[0]:
        print(f"❌ Background thread test failed: {error_msg[0]}")
        return False
    
    print("🎉 All thread-safe manager tests passed!")
    return True

def test_pipeline_step():
    """Test a single step of the pipeline in a thread."""
    
    print("\n🔬 Testing Pipeline Step in Thread")
    print("=" * 50)
    
    success = [False]
    error_msg = [None]
    
    def run_pipeline_step():
        try:
            # Import required modules
            from streamlit_app.utils.database import get_article_manager_for_thread, get_extraction_manager_for_thread
            from src.agents.disease_guardrail import validate_disease_name
            
            print("Step 1: Getting database managers...")
            article_manager = get_article_manager_for_thread()
            extraction_manager = get_extraction_manager_for_thread()
            
            if not article_manager or not extraction_manager:
                error_msg[0] = "Failed to get database managers"
                return
            
            print("✅ Database managers obtained")
            
            print("Step 2: Validating disease name...")
            validation_result = validate_disease_name("ulcerative colitis")
            
            if not validation_result["is_disease"]:
                error_msg[0] = f"Disease validation failed: {validation_result['explanation']}"
                return
            
            print("✅ Disease validation passed")
            
            print("Step 3: Checking existing articles...")
            existing_count = article_manager.count_existing_articles_for_disease("ulcerative colitis")
            print(f"✅ Found {existing_count} existing articles")
            
            success[0] = True
            
        except Exception as e:
            error_msg[0] = str(e)
            import traceback
            traceback.print_exc()
    
    # Run in background thread
    thread = threading.Thread(target=run_pipeline_step, daemon=True)
    thread.start()
    thread.join(timeout=15)
    
    if not success[0]:
        print(f"❌ Pipeline step test failed: {error_msg[0]}")
        return False
    
    print("🎉 Pipeline step test passed!")
    return True

if __name__ == "__main__":
    print("🚀 Starting Threading Fix Tests")
    print("=" * 60)
    
    # Test thread-safe managers
    if not test_thread_safe_managers():
        print("\n❌ Thread-safe manager tests failed. Exiting.")
        sys.exit(1)
    
    # Test pipeline step
    if not test_pipeline_step():
        print("\n❌ Pipeline step test failed. Exiting.")
        sys.exit(1)
    
    print("\n🎉 All threading tests passed! The fix should work.")
