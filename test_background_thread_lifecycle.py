#!/usr/bin/env python3
"""
Test script to verify that the background thread lifecycle is working correctly.
"""

import sys
import os
import threading
import time

# Add src directory to Python path
sys.path.append('src')

def test_background_thread_lifecycle():
    """Test that the background thread continues running and doesn't terminate early."""
    
    print("🧪 Testing Background Thread Lifecycle")
    print("=" * 50)
    
    # Import the progress manager and pipeline function
    from streamlit_app.utils.progress_manager import get_progress_manager
    from streamlit_app.pages.research_interface import run_research_pipeline_thread_safe
    
    # Get progress manager
    progress_manager = get_progress_manager()
    progress_manager.reset_progress()
    
    # Test parameters
    disease_name = "diabetes"
    country = "US"
    time_period = "2020-2025"
    num_works = 2  # Small number for testing
    
    print(f"Testing pipeline with: {disease_name}, {country}, {time_period}, {num_works} works")
    
    # Set initial state
    progress_manager.set_running(disease_name, country, total_steps=6)
    
    # Track thread lifecycle
    thread_started = False
    thread_completed = False
    thread_exception = None
    
    def run_pipeline_thread():
        nonlocal thread_started, thread_completed, thread_exception
        try:
            thread_started = True
            print("🚀 Background thread started...")
            
            # Add some logging to track progress
            def log_progress():
                while progress_manager.get_progress()['is_running']:
                    progress = progress_manager.get_progress()
                    print(f"   Thread alive - Step: {progress['current_step']} ({progress['completed_steps']}/{progress['total_steps']})")
                    time.sleep(1)
            
            # Start progress logging in a separate thread
            log_thread = threading.Thread(target=log_progress, daemon=True)
            log_thread.start()
            
            # Run the actual pipeline
            run_research_pipeline_thread_safe(disease_name, country, time_period, num_works)
            
            thread_completed = True
            print("✅ Background thread completed successfully!")
            
        except Exception as e:
            thread_exception = e
            print(f"❌ Background thread failed: {e}")
            import traceback
            traceback.print_exc()
    
    # Start the pipeline thread
    pipeline_thread = threading.Thread(target=run_pipeline_thread, daemon=True)
    pipeline_thread.start()
    
    # Monitor the thread for a reasonable amount of time
    print("\n📊 Monitoring thread lifecycle...")
    max_wait_time = 120  # 2 minutes max
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        if not pipeline_thread.is_alive():
            if thread_completed:
                print("✅ Thread completed normally")
                break
            elif thread_exception:
                print(f"❌ Thread terminated with exception: {thread_exception}")
                break
            else:
                print("⚠️ Thread terminated unexpectedly without completion or exception")
                break
        
        # Check progress
        progress = progress_manager.get_progress()
        elapsed = time.time() - start_time
        print(f"Time: {elapsed:.1f}s - Thread alive: {pipeline_thread.is_alive()} - Running: {progress['is_running']} - Step: {progress['current_step']}")
        
        time.sleep(2)
    
    # Final check
    if pipeline_thread.is_alive():
        print("⚠️ Thread still running after timeout - this might indicate a hang")
        pipeline_thread.join(timeout=5)
    
    # Final state
    final_state = progress_manager.get_progress()
    print(f"\n📋 Final thread state:")
    print(f"   Thread started: {thread_started}")
    print(f"   Thread completed: {thread_completed}")
    print(f"   Thread exception: {thread_exception}")
    print(f"   Pipeline running: {final_state['is_running']}")
    print(f"   Current step: {final_state['current_step']}")
    print(f"   Completed steps: {final_state['completed_steps']}/{final_state['total_steps']}")
    print(f"   Errors: {len(final_state['errors'])}")
    
    if final_state['errors']:
        print("❌ Pipeline errors:")
        for error in final_state['errors']:
            print(f"   - {error}")
    
    # Determine success
    if thread_started and thread_completed and not thread_exception:
        print("✅ Background thread lifecycle test PASSED!")
        return True
    else:
        print("❌ Background thread lifecycle test FAILED!")
        return False

if __name__ == "__main__":
    print("🚀 Starting Background Thread Lifecycle Test")
    print("=" * 60)
    
    success = test_background_thread_lifecycle()
    
    if success:
        print("\n🎉 Background thread lifecycle test completed successfully!")
    else:
        print("\n❌ Background thread lifecycle test failed!")
        sys.exit(1)
